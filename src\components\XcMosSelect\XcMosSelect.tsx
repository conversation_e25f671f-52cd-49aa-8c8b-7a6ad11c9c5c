import { useEffect, useMemo, useRef, useState } from 'react';
import { getAllMosqutio } from '@/utils/mosqutio.util';
import { permissionApi } from '@/api';
import { request, styled, useModel } from '@umijs/max';
import { Select } from 'antd';
import { IconArrowDown } from '@/assets/svg';
import { useMemoizedFn } from 'ahooks';

const StyledSelect = styled(Select)`
  width: 120px !important;
  .ant-select-selection-item-content,
  .ant-select-selection-item {
    background: transparent !important;
    padding: 0 !important;
    margin: 0 !important;
  }
`;
const ALL_KEY = 'all';
const XcMosSelect = (props: any) => {
  const { onSelect: propOnSelect, onChange: propOnChange, ...others } = props;
  const { mosquitoList } = useModel('useMosquito', (m) => {
    return {
      mosquitoList: m.mosquitoList,
    };
  });
  const [options, setOptions] = useState<any>([{ name: '全部种类', code: ALL_KEY }]);
  const [selectedValues, setSelectedValues] = useState<string[]>([ALL_KEY]);

  useEffect(() => {
    setOptions([{ name: '全部种类', code: ALL_KEY }, ...mosquitoList]);
  }, [mosquitoList]);

  const onSelectOption = useMemoizedFn((value: any) => {
    propOnSelect?.(value);
    if (value === ALL_KEY) {
      setSelectedValues([ALL_KEY]);
      propOnChange?.([ALL_KEY]);
    } else {
      setSelectedValues((prev: string[]) => {
        let result = [...prev, value];
        result = result.filter((item) => item !== ALL_KEY);
        propOnChange?.(result);
        return result;
      });
    }
  });
  const onDeselectOption = (value: any) => {
    setSelectedValues((prev: string[]) => {
      let result = prev.filter((item) => item !== value);
      propOnChange?.(result);
      return result;
    });
  };
  return (
    <StyledSelect
      defaultValue={[ALL_KEY]}
      mode="multiple"
      showSearch={false}
      value={selectedValues}
      fieldNames={{ label: 'name', value: 'code' }}
      suffixIcon={<IconArrowDown className="text-[#999]" />}
      options={options}
      onSelect={onSelectOption}
      onDeselect={onDeselectOption}
      maxTagCount={0}
      maxTagPlaceholder={(values: any) => {
        const text = values.map((item: any) => item.label).join(', ');
        return (
          <div className="w-[80px] text-one-row pl-[10px]" title={text}>
            {text}
          </div>
        );
      }}
      {...others}
    />
  );
};
export default XcMosSelect;
