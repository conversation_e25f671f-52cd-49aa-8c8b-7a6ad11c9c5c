import dayjs from 'dayjs';

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-12 16:08:25
 * @LastEditTime: 2024-11-12 16:08:32
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const DATE_FMT = {
  DATETIME_LOCAL: 'YYYY-MM-DDTHH:mm',
  DATETIME_LOCAL_SECONDS: 'YYYY-MM-DDTHH:mm:ss',
  DATETIME_LOCAL_MS: 'YYYY-MM-DDTHH:mm:ss.SSS',
  MONTH: 'YYYY-MM',
  YEAR: 'YYYY',
  DAY: 'YYYY-MM-DD',
  TIME: 'HH:mm',
  TIME_SECONDS: 'HH:mm:ss',
  DATE_TIME: 'YYYY-MM-DD HH:mm:ss',
};

export enum TIME_UNITS {
  MONTH = 'month',
  WEEK = 'week',
  DAY = 'day',
  HOUR = 'hour',
}

export const TIME_UNITS_LIST = [
  {
    label: '小时',
    value: TIME_UNITS.HOUR,
  },
  {
    label: '天',
    value: TIME_UNITS.DAY,
  },
  {
    label: '周',
    value: TIME_UNITS.WEEK,
  },

  {
    label: '月',
    value: TIME_UNITS.MONTH,
  },
];

export function getRangeByTimeUnit(unit?: TIME_UNITS): [dayjs.Dayjs, dayjs.Dayjs] {
  const timeUnit = unit || TIME_UNITS.MONTH;
  if (timeUnit === TIME_UNITS.MONTH) {
    return [dayjs().subtract(1, 'month').add(1, 'day').startOf('day'), dayjs().endOf('day')];
  } else if (timeUnit === TIME_UNITS.WEEK) {
    return [dayjs().subtract(6, 'day').startOf('day'), dayjs().endOf('day')];
  } else if (timeUnit === TIME_UNITS.DAY) {
    return [dayjs().startOf('day'), dayjs().endOf('day')];
  } else {
    return [dayjs().startOf('day'), dayjs().endOf('day')];
  }
}

export function getRangeByTimeUnitAndFormat(
  unit?: TIME_UNITS,
  format?: (typeof DATE_FMT)[keyof typeof DATE_FMT],
): [string, string] {
  const range = getRangeByTimeUnit(unit);
  return range.map((item: dayjs.Dayjs) => item.format(format)) as [string, string];
}
