/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-28 08:42:22
 * @LastEditTime: 2025-02-06 19:59:35
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { styled } from '@umijs/max';
import { Button, ConfigProvider } from 'antd';
import { useMemo } from 'react';

interface IProps {
  className?: string;
  fontClassName?: string;
  icon?: React.ReactNode;
  children?: React.ReactNode;
  onClick?: () => void;
  type?: 'default' | 'primary';
  bgColor?: string;
}

const StyledButton = styled(Button)`
  .ant-btn-icon {
    display: flex;
    align-items: center;
  }
`;

export const XcButton = (props: IProps) => {
  const { bgColor, type = 'default', className, fontClassName, icon, children, onClick } = props;

  let color = useMemo(() => {
    '#F4F4F6';
    if (bgColor) {
      return bgColor;
    } else {
      if (type === 'primary') {
        return '#0080FF';
      } else if (type === 'default') {
        return '#F4F4F6';
      }
    }
  }, []);

  return (
    <ConfigProvider
      theme={{
        components: {
          Button: {
            colorBgContainer: color,
          },
        },
      }}
    >
      <StyledButton className={className} icon={icon} onClick={onClick}>
        <span className={`!self-center ${fontClassName ?? ''}`}>{children}</span>
      </StyledButton>
    </ConfigProvider>
  );
};
