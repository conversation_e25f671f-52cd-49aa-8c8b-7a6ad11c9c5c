import { useGlobalStore } from '@/store/globalStore';
import { ReactComponent as ArrowRight } from '@/assets/svg/ArrowRight.svg';
import { useModel } from '@umijs/max';
import { useMemoizedFn } from 'ahooks';
import XxcBlurHide from '../XxcBlurHide';
import ReactDom from 'react-dom';
import './index.css';
export default function XxcUserPopover(props: { visible: boolean; onClose: () => void }) {
  const { visible, onClose } = props;
  const { logout } = useModel('user');
  const { togglePasswordModalVisible } = useGlobalStore();
  const { getPersist } = useModel('persist');
  const user = getPersist('user');
  const onLogout = useMemoizedFn(() => {
    logout();
    onClose();
  });

  const onShowPassword = useMemoizedFn(() => {
    togglePasswordModalVisible(true);
    onClose();
  });

  return ReactDom.createPortal(
    <XxcBlurHide visible={visible} onHide={onClose}>
      <div className="XxcUserPopover">
        <div className="XxcUserPopover-user">
          <div className="XxcUserPopover-userAvatar">
            <img src={user?.avatar_img} className="XxcUserPopover-userAvatarImg" />
          </div>
          <div className="XxcUserPopover-userInfo">
            <div className="XxcUserPopover-username">{user?.username}</div>
            <div className="XxcUserPopover-userRole">{user?.role_names?.[0]}</div>
          </div>
        </div>
        <div className="XxcUserPopover-menu">
          <div className="XxcUserPopover-menuItem" onClick={onShowPassword}>
            修改密码
            <ArrowRight className="XxcUserPopover-menuItemIcon" />
          </div>
        </div>
        <div className="XxcUserPopover-logout" onClick={onLogout}>
          退出登录
        </div>
      </div>
    </XxcBlurHide>,
    document.body,
  );
}
