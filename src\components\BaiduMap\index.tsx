// import { forwardRef, lazy, Suspense, useEffect, useState } from 'react';
// import { loadBaiduMapApi } from '@/utils/loader.util';
import { BaiduMap } from '@/components/BaiduMap/BaiduMap';
// const BaiduMap = lazy(() => {
//   return import('@/components/BaiduMap/BaiduMap').then(({ BaiduMap }) => ({ default: BaiduMap }));
// });

// export default forwardRef((props: any, ref: any) => {
//   const [inited, setInited] = useState(false);
//   useEffect(() => {
//     loadBaiduMapApi().then(() => {
//       setInited(true);
//       console.log(window.BMapGL);
//     });
//   }, []);
//   if (!inited) {
//     return null;
//   }
//   return (
//     <Suspense fallback={<div data-name="BaiduMap"></div>}>
//       <BaiduMap ref={ref} {...props}>
//         {props.children}
//       </BaiduMap>
//     </Suspense>
//   );
// });
export default BaiduMap;
