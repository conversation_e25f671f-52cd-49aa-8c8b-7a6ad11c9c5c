import React, { useEffect, useMemo } from 'react';
import Panel from '@/components/Panel';
import { useRequest, history } from '@umijs/max';
import { statisticsAPI } from '@/api';
import { Spin } from 'antd';

const AreaRankingPanel: React.FC<any> = (props) => {
  const { startTime, endTime, range, updatePage, mosquitoType } = props;
  const {
    run: getRegionRank,
    data: regionRankRes,
    loading,
  } = useRequest(
    (data?: any, headers?: any) => ({
      url: statisticsAPI.getRegionRank,
      method: 'GET',
      params: { ...data, tenant_id: 1 },
      headers,
    }),
    {
      manual: true,
      throwOnError: true,
    },
  );

  useEffect(() => {
    if (typeof range?.province === 'undefined') return;
    getRegionRank({
      top_n: 5,
      start_time: startTime,
      end_time: endTime,
      ...range,
      monitor_type: mosquitoType,
    });
  }, [range, mosquitoType]);

  const gotoAnalysis = () => {
    history.push('/data/analysis/region');
  };

  const regionRank = useMemo(() => {
    return regionRankRes?.regions || [];
  }, [regionRankRes?.regions]);

  useEffect(() => {
    let totalCount = 0;
    regionRank.forEach((item: any) => {
      totalCount += item.total_count || 0;
    });
    updatePage({ totalCount });
  }, [regionRank]);
  return (
    <Panel
      title={
        <span className="flex items-center gap-2">
          <span>蚊虫高发区域Top5</span>
          <div className="text-[12px] text-[#999]">（该模块展示的是监测蚊总数最多的五个区域）</div>
        </span>
      }
      actions={
        <div onClick={gotoAnalysis} className=" cursor-pointer">
          <svg width="20" height="20" fill="none">
            <circle cx="4" cy="10" r="2" fill="#797A85" />
            <circle cx="10" cy="10" r="2" fill="#797A85" />
            <circle cx="16" cy="10" r="2" fill="#797A85" />
          </svg>
        </div>
      }
      contentStyle={{
        overflow: 'visible',
      }}
    >
      <div className="w-full h-full flex flex-col">
        <table className="w-full text-[12px] text-left">
          <thead>
            <tr className="text-[#797A85] border-b border-[#F0F0F0] font-[500]">
              <th className="py-[8px] font-[400]">排名</th>
              <th className="py-[8px] font-[400]">监测区域</th>
              <th className="py-[8px] font-[400]">蚊虫监测总数（只）</th>
              <th className="py-[8px] font-[400]">该区域监测数占总蚊虫数量比例（%）</th>
            </tr>
          </thead>
          <tbody>
            {regionRank.length > 0
              ? regionRank?.map((row: any, idx: any) => (
                  <tr
                    key={idx}
                    className={'border-b border-[#F0F0F0] last:border-0 text-[#1F2134]'}
                  >
                    <td className="py-[8px] pr-[8px]  font-[500] w-[32px] ">
                      {row.rank === 1 ? (
                        <span className="text-[#ED8061]">{row.rank}</span>
                      ) : row.rank === 2 ? (
                        <span className="text-[#F0B261]">{row.rank}</span>
                      ) : row.rank === 3 ? (
                        <span className="text-[#3381F8]">{row.rank}</span>
                      ) : (
                        <span className="text-[#1F2134]">{row.rank}</span>
                      )}
                    </td>
                    <td className="py-[8px] pr-[8px] w-[80px]">{row.region}</td>
                    <td className="py-[8px] pr-[8px] w-[96px]">{row.total_count}</td>
                    <td className="py-[8px] w-[160px]">
                      <div className="flex items-center gap-[8px]">
                        <div className="flex-1 bg-[#EDEFF7] rounded h-[6px] relative">
                          <div
                            className="bg-[#68DBAD] h-[6px] rounded"
                            style={{ width: `${row.ratio}%` }}
                          ></div>
                        </div>
                        <span className="text-[#1F2134] w-[64px] text-left">{row.ratio}%</span>
                      </div>
                    </td>
                  </tr>
                ))
              : null}
          </tbody>
        </table>
        {regionRank.length > 0 ? null : (
          <div className="flex-1 flex items-center justify-center text-txt-sub">
            {loading ? <Spin /> : '无可用监测数据'}
          </div>
        )}
      </div>
    </Panel>
  );
};

export default AreaRankingPanel;
