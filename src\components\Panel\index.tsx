import React from 'react';

interface PanelProps {
  title: React.ReactNode;
  actions?: React.ReactNode;
  children: React.ReactNode;
  contentStyle?: React.CSSProperties;
  boxStyle?: React.CSSProperties;
}

const Panel: React.FC<PanelProps> = ({ title, actions, children, contentStyle, boxStyle }) => {
  return (
    <div
      className="bg-white rounded-[12px] border border-[#E4E6EB] shadow-[0_4px_12px_0_rgba(96,97,132,0.08)] p-0 w-full h-full flex flex-col  "
      style={boxStyle}
    >
      <div className="box-border flex items-center justify-between mb-0 px-[20px] py-[9px] border-b border-[#F0F0F0] rounded-t-[12px] h-[50px]">
        <div className=" text-[16px] font-medium text-[#1F2134] ">{title}</div>
        {actions}
      </div>
      <div
        className="flex-1 min-h-0 px-[24px] py-[14px] xc-scrollbar-y flex flex-col"
        style={contentStyle}
      >
        {children}
      </div>
    </div>
  );
};

export default Panel;
