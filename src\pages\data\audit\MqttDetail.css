.MqttDetailDrawer {
  --bg-color: rgba(246, 246, 248, 0.7);

  .ant-drawer-body {
    padding: 8px 20px;
  }

  .MqttDetail-head {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px;
    line-height: 24px;
    font-size: 16px;
    height: 56px;
    border-radius: 8px;
    background: var(--bg-color);
  }

  .MqttDetail-device {
    margin: 20px 0 12px 0;
    font-size: 16px;
    font-weight: 500;
    line-height: 20px;
    color: var(--txt-main);
    display: flex;
    align-items: center;
  }

  .MqttDetail-tag {
    line-height: 20px;
    padding: 0px 4px;
    background: var(--bg-color);
    border-radius: 4px;
    font-size: 12px;
    color: var(--txt-sub);
    margin-left: 12px;
  }

  .DataBlock {
    color: var(--txt-main);
    background: var(--bg-color);
    padding: 10px 12px;
    border-radius: 4px;
    margin-bottom: 12px;
    min-width: 0;
  }

  .is-data-position {
    color: var(--txt-third);
  }

  .VerifyProgressItem {
    position: relative;
    font-size: 14px;
    line-height: 24px;
    color: var(--txt-main);
    margin-top: 8px;
    background: #fff;
    border-radius: 4px;
    padding: 8px;
  }

  .VerifyProgressItem-result {
    display: flex;
    line-height: 20px;
    flex-wrap: wrap;
    font-weight: 600;
  }

  .VerifyProgressItem-arrow {
    position: absolute;
    right: 8px;
    top: 12px;
    cursor: pointer;
    width: 12px;
    height: 12px;
  }

  .DataTabListBox {
    overflow-x: hidden;
    position: relative;
    flex: 1;
  }

  .DataTabListBox-left {
    position: absolute;
    left: 0;
    top: 0;
    height: 32px;
    width: 4px;
    background: white;
    box-shadow: 2px 0px 4px 0px rgba(201, 206, 228, 0.21);
  }

  .DataTabListBox-right {
    position: absolute;
    right: 0;
    top: 0;
    height: 32px;
    width: 4px;
    background: white;
    box-shadow: -2px 0px 4px 0px rgba(201, 206, 228, 0.21);
  }

  .DataTabList {
    display: flex;
    gap: 0 16px;
    align-items: center;
  }

  .DataTabItem {
    position: relative;
    height: 26px;
    line-height: 20px;
    text-align: center;
    cursor: pointer;
    color: var(--txt-third);

    &.is-active {
      color: var(--txt-main);
    }

    &.is-active::after {
      position: absolute;
      content: '';
      left: 4px;
      right: 4px;
      bottom: 0;
      height: 2px;
      background-color: var(--btn-blue);
      border-radius: 2px;
    }
  }

  .VerifyItem-remove {
    width: 20px;
    height: 20px;
    cursor: pointer;
    color: var(--txt-third);
  }

  .RecognitionResult-head {
    height: 26px;
    display: flex;
    align-items: center;
    justify-content: space-between;
  }

  .RecognitionResult-verifyBtn {
    background-color: var(--btn-blue);
    width: 68px;
    height: 26px;
    line-height: 26px;
    border-radius: 4px;
    color: #fff;
    font-size: 12px;
    text-align: center;
    cursor: pointer;
  }

  .RecognitionResult-desc {
    display: flex;
    align-items: center;
    margin-top: 12px;
    font-size: 14px;
    font-weight: normal;
    height: 20px;
    color: var(--txt-sub);
  }

  .RecognitionResult-plainText {
    background: #fff;
    color: var(--txt-main);
    padding: 8px;
    border-radius: 4px;
    margin-top: 8px;
  }

  .RecognitionResult-addBtn {
    width: 100%;
    height: 40px;
    margin-top: 8px;
    border-radius: 8px;
    align-items: center;
    justify-content: center;
    display: flex;
    padding: 0px 12px;
    background: linear-gradient(0deg, rgba(51, 85, 255, 0.03), rgba(51, 85, 255, 0.03)), #FFFFFF;
    box-sizing: border-box;
    border: 0.5px dashed var(--btn-blue);
    color: var(--btn-blue);
    cursor: pointer;
    .plusIcon {
      margin-right: 6px;
      width: 16px;
      height: 16px;
    }
  }
}
