import { role<PERSON><PERSON> } from '@/api/role';
import { XcModalForm } from '@/components/XcModal/XcModalForm';
import { ProForm } from '@ant-design/pro-components';
import { request, useRequest } from '@umijs/max';
import { Button, Form, message, Table, Typography } from 'antd';
import { useEffect, useRef, useState } from 'react';
import { RoleAddOrEdit } from './components/RoleAddOrEdit';
import { XcInput } from '@/components/XcInput';
import { ColumnsType } from 'antd/es/table';
import { permissionApi } from '@/api/permission';
import XcAntdTable from '@/components/XcAntdTable/XcAntdTable';
import { IconClose } from '@/components/Icon/IconClose';
import { IconSearch } from '@/components/Icon/IconSearch';
import PermissionWrap from '@/components/PermissionWrap';
import { STATIC_PERMISSION_CODE } from '@/utils/permission';
import { IconAdd } from '@/assets/svg';

const { Text } = Typography;
enum MODAL_TYPE {
  none = 0,
  add = 1,
  edit = 2,
  delete = 3,
  unbind = 4, // 解绑
}
export default () => {
  const [modalType, setModalType] = useState<MODAL_TYPE>(MODAL_TYPE.none);
  const [currentItem, setCurrentItem] = useState<Record<string, any>>({});
  const [dataSource, setDataSource] = useState<any>([]);
  const [dataPermissionList, setDataPermissionList] = useState<any>([]);
  const [menuPermissionList, setMenuPermissionList] = useState<any>([]);
  const formRef = useRef<any>(null);
  const paginationRef = useRef({
    current: 1,
    pageSize: 50,
    total: 0,
  });

  const roleListRequest = useRequest(
    (data?: any, headers?: any) => ({
      url: roleApi.getList,
      method: 'GET',
      params: { ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: { total: 0, page: 1, pageSize: 50, list: [] } },
      throwOnError: true,
    },
  );
  const requestTable = async () => {
    const name = formRef.current?.getFieldValue('name');
    const { current, pageSize } = paginationRef.current;
    const queryPayload = {
      name,
      page: current,
      page_size: pageSize,
      tenant_id: 1,
    };
    const result = await roleListRequest.run(queryPayload);

    if (result.code === 200) {
      paginationRef.current.total = result?.data?.total || 0;
      setDataSource(result.data.list);
    } else {
      setDataSource([]);
    }
  };
  const getDataPermissionList = async () => {
    const result = await request(permissionApi.getDataPermissionList, {
      method: 'GET',
      params: {
        tenant_id: 1,
        type: 'mosquito',
        // level: 3,
      },
    });
    const list = result?.data?.list;
    if (Array.isArray(list)) {
      setDataPermissionList(list || []);
      return list;
    }
    return [];
  };
  const getMenuPermissionList = async () => {
    const result = await request(permissionApi.getMenuPermissionList, {
      method: 'GET',
      params: {
        tenant_id: 1,
      },
    });
    const list = result?.data?.list;
    if (Array.isArray(list)) {
      setMenuPermissionList(list);
      return list;
    }
    return [];
  };

  useEffect(() => {
    getMenuPermissionList();
    getDataPermissionList();
    requestTable();
  }, []);

  const closeModal = () => {
    setCurrentItem({});
    setModalType(MODAL_TYPE.none);
  };
  const onOpenModal = (open: boolean) => {
    if (!open) {
      closeModal();
    }
  };

  const showModal = async (item: any, type: MODAL_TYPE) => {
    setCurrentItem(item);
    if (type === MODAL_TYPE.edit) {
      const result = await request(roleApi.getRole(item.id), {
        method: 'GET',
        params: {
          tenant_id: 1,
        },
      });
      if (result.code === 200) {
        setCurrentItem(result.data);
      }
    }

    setModalType(type);
  };

  const onDeleteRole = async () => {
    return request(roleApi.deleteRole(currentItem.id), {
      method: 'DELETE',
      params: {
        tenant_id: 1,
      },
    }).then((res: any) => {
      if (res.code === 200) {
        message.info('删除成功');
        closeModal();
        requestTable();
        return true;
      } else {
        message.error(res.message || '删除失败');
        return true;
      }
    });
  };

  const onUnbindRole = async () => {
    return request(roleApi.deleteBindRole(currentItem.id), {
      method: 'DELETE',
      params: {
        tenant_id: 1,
      },
    }).then((res: any) => {
      if (res.code === 200) {
        message.info('解绑成功');
        closeModal();
        requestTable();
        return true;
      } else {
        message.info(res.message || '解绑失败');
        return false;
      }
    });
  };
  const columns: any = [
    {
      title: '角色名称',
      dataIndex: 'name',
      key: 'name',
      hideInSearch: true,
      render: (_, item: { name: string }) => {
        return (
          <Text className="text-txt-main" ellipsis={{ tooltip: item.name }}>
            {item.name}
          </Text>
        );
      },
    },
    {
      title: '角色编号',
      dataIndex: 'code',
      key: 'code',
      link: true,
      hideInSearch: true,
      render: (_, item: any) => {
        return item.code;
      },
    },
    {
      title: '角色用户数',
      dataIndex: 'user_count',
      key: 'user_count',
      hideInSearch: true,
      render: (_, item: { user_count: string }) => {
        return (
          <Text className="text-txt-main" ellipsis={{ tooltip: item.user_count }}>
            {item.user_count}
          </Text>
        );
      },
    },

    {
      title: '操作',
      width: 154,
      link: true,
      align: 'center',
      hideInSearch: true,
      render: (_, item: any) => {
        return (
          <div className={'flex items-center justify-end gap-[16px] w-full'}>
            <PermissionWrap accessCode={STATIC_PERMISSION_CODE.解绑角色用户}>
              <Button
                type="link"
                className="text-txt-main px-[0px]"
                onClick={() => showModal(item, MODAL_TYPE.unbind)}
                hidden={item.user_count <= 0}
              >
                解绑
              </Button>
            </PermissionWrap>
            <PermissionWrap accessCode={STATIC_PERMISSION_CODE.更新角色}>
              <Button
                type="link"
                className="text-txt-main px-[0px]"
                onClick={() => showModal(item, MODAL_TYPE.edit)}
              >
                修改
              </Button>
            </PermissionWrap>
            <PermissionWrap accessCode={STATIC_PERMISSION_CODE.删除角色}>
              <Button
                type="link"
                className="text-txt-main px-[0px]"
                onClick={() => showModal(item, MODAL_TYPE.delete)}
              >
                删除
              </Button>
            </PermissionWrap>
          </div>
        );
      },
    },
  ];

  return (
    <div className="flex-1 flex ">
      <div className="w-full   mt-[20px]">
        <div className="flex">
          <div className="flex-1">
            <ProForm
              layout="horizontal"
              formRef={formRef}
              submitter={false}
              onFinish={requestTable}
            >
              <Form.Item name="name" className="mb-[20px]">
                <XcInput
                  placeholder="搜索角色名称"
                  prefix={<IconSearch className="ml-[16px] mr-[8px] size-[20px] text-txt-sub " />}
                  allowClear={{
                    clearIcon: (
                      <IconClose
                        onClick={() => {
                          requestTable();
                        }}
                      />
                    ),
                  }}
                  style={{
                    width: '320px',
                    height: '40px',
                    borderRadius: '8px',
                  }}
                  onPressEnter={requestTable}
                  onBlur={requestTable}
                  onChange={requestTable}
                />
              </Form.Item>
            </ProForm>
          </div>
          <PermissionWrap accessCode={STATIC_PERMISSION_CODE.创建角色}>
            <Button
              type="primary"
              className={'w-[124px] h-[40px] rounded-[8px] bg-btn-blue text-txt-white text-[14px]'}
              icon={<IconAdd className="!align-middle" />}
              onClick={() => {
                showModal({}, MODAL_TYPE.add);
              }}
            >
              新增角色
            </Button>
          </PermissionWrap>
        </div>
        <div className="h-[calc(100vh-70px-60px-80px-32px)]">
          <XcAntdTable
            loading={roleListRequest.loading}
            columns={columns as ColumnsType}
            pagination={{
              ...paginationRef.current,
              onChange: (page: number, pageSize: number) => {
                paginationRef.current.current = page;
                paginationRef.current.pageSize = pageSize;
                requestTable();
              },
            }}
            dataSource={dataSource}
          />
        </div>
      </div>
      {modalType === MODAL_TYPE.add || modalType === MODAL_TYPE.edit ? (
        <RoleAddOrEdit
          modalType={modalType}
          onClose={closeModal}
          queryTableData={requestTable}
          currentItem={currentItem}
          onOpenChange={onOpenModal}
          dataPermissionList={dataPermissionList}
          menuPermissionList={menuPermissionList}
        ></RoleAddOrEdit>
      ) : null}
      <XcModalForm
        title="确认"
        width={500}
        open={modalType === MODAL_TYPE.delete}
        modalProps={{
          zIndex: 1000,
          destroyOnClose: true,
          centered: true,
        }}
        onOpenChange={onOpenModal}
        onFinish={onDeleteRole}
      >
        <div className="my-[40px]">删除“{currentItem.name}”后，绑定该角色的用户将失去相应权限</div>
      </XcModalForm>
      <XcModalForm
        title="解绑"
        width={500}
        open={modalType === MODAL_TYPE.unbind}
        modalProps={{
          zIndex: 1000,
          destroyOnClose: true,
          centered: true,
        }}
        onOpenChange={onOpenModal}
        onFinish={onUnbindRole}
      >
        <div className="my-[40px]">
          解绑“{currentItem.name}”角色后，该角色下的用户（{currentItem.user_count}
          人）将无法查看相关数据，请谨慎操作！确定解绑？
        </div>
      </XcModalForm>
    </div>
  );
};
