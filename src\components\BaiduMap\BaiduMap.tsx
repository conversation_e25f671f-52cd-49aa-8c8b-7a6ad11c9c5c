import { regxUtil } from '@/utils/regx.util';
import { useFullscreen } from 'ahooks';
import { forwardRef, memo, useEffect, useImperativeHandle, useRef } from 'react';
import { IBaiduMapOptions, IBaiduMapPoi } from './baidu-map.interface';
import './BaiduMap.less';
import { FullScreenControl } from './XcFullScreenControl';
import { createXcPoint } from './XcPoint';

interface IPoint {
  icon: any;
  longitude: number;
  latitude: number;
  status: number;
  width: number;
  height: number;
  [key: string]: any;
  onClick?: (data: any) => void;
}

interface IProps {
  heading?: number;
  tilt?: number;
  // 名称
  name?: string;
  longitude?: number;
  latitude?: number;
  zoom?: number;
  mapType?: 'BMAP_NORMAL_MAP' | 'BMAP_EARTH_MAP' | 'BMAP_SATELLITE_MAP';
  points?: IPoint[];
  children?: React.ReactNode;
}

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-15 20:35:17
 * @LastEditTime: 2025-01-15 20:49:14
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const BaiduMap = memo(
  forwardRef((props: IProps, ref: any) => {
    const { latitude, longitude, points, name, zoom = 15, children } = props;
    const containerRef = useRef(null);
    const bmapRef = useRef<any>(null);

    const [isFullscreen, { toggleFullscreen }] = useFullscreen(containerRef);

    const addPoints = (point: any) => {
      if (!point) {
        return;
      }
      bmapRef.current.clearOverlays();
      const devicePoints = point.filter((item: any) => item.gps);
      for (let i = 0; i < devicePoints?.length; i++) {
        const { onClick, ...rest } = devicePoints[i];
        const [lng, lat] = rest.gps.split(',');

        if (rest.gps && lng && lat && regxUtil.poi.test(lng) && regxUtil.poi.test(lat)) {
          let customOverlay = new (BMapGL as any).CustomOverlay(createXcPoint, {
            point: new BMapGL.Point(lng, lat),
            properties: {
              width: rest.width ?? 30,
              height: rest.height ?? 30,
              imgSrc: rest.url,
              onClick: () => {
                if (onClick) {
                  onClick({ ...rest });
                }
              },
            },
          });
          bmapRef.current.addOverlay(customOverlay);
        }
      }
    };

    const initMap = ({ name, lng, lat }: { name?: string; lng?: number; lat?: number }) => {
      if (containerRef.current && BMapGL) {
        // GL版命名空间为BMapGL
        // 按住鼠标右键，修改倾斜角和角度
        bmapRef.current = new BMapGL.Map(containerRef.current, {
          minZoom: 3,
          maxZoom: 20,
        });

        // bmapRef.current.setHeading(64.5); // 设置地图旋转角度
        // bmapRef.current.setTilt(73); // 设置地图倾斜角度

        // 创建Map实例
        bmapRef.current.enableScrollWheelZoom(); //开启鼠标滚轮缩放

        if (name) {
          bmapRef.current.centerAndZoom(name, zoom); // 初始化地图,设置中心点坐标和地图级别
          requestAnimationFrame(() => {
            bmapRef.current.setCenter(name);
          });
        } else if (
          lng &&
          lat &&
          regxUtil.poi.test(lng.toString()) &&
          regxUtil.poi.test(lat.toString())
        ) {
          const center = new BMapGL.Point(Number(lng), Number(lat));
          bmapRef.current.centerAndZoom(center, zoom); // 初始化地图,设置中心点坐标和地图级别
          requestAnimationFrame(() => {
            bmapRef.current.setCenter(center);
          });
        }

        // 创建自定义控件的DOM元素
        let myZoomCtrl = new FullScreenControl(() => {
          toggleFullscreen();
        });
        bmapRef.current.addControl(myZoomCtrl);

        // 添加3D控件
        let navi3DCtrl = new BMapGL.NavigationControl3D({
          anchor: BMAP_ANCHOR_BOTTOM_RIGHT,
          // 20 + 36 + 20
          offset: new BMapGL.Size(10, 76),
        });

        bmapRef.current.addControl(navi3DCtrl);

        // points
        if (points) {
          addPoints(points);
        }

        return () => {
          if (bmapRef.current) {
            (bmapRef.current as any).destroy();
          }
        };
      }
    };

    //设置中心点
    const chargeCenterByName = (name: string, options: IBaiduMapOptions) => {
      if (bmapRef.current) {
        const { zoom } = options;
        requestAnimationFrame(() => {
          bmapRef.current.centerAndZoom(name, zoom);
        });
      } else {
        initMap({ name });
      }
    };
    const chargeCenterByPoi = (poi: IBaiduMapPoi, options: IBaiduMapOptions) => {
      const { lng, lat } = poi;
      if (
        bmapRef.current &&
        lng &&
        lat &&
        regxUtil.poi.test(lng.toString()) &&
        regxUtil.poi.test(lat.toString())
      ) {
        const { zoom } = options;
        const center = new BMapGL.Point(lng, lat);

        bmapRef.current.flyTo(center, zoom);
      } else {
        initMap({ name });
      }
    };

    // 初始化
    useEffect(() => {
      initMap({ name, lng: longitude, lat: latitude });
    }, []);
    // 更新点位
    useEffect(() => {
      if (bmapRef.current && points) {
        // 清除原有点位
        bmapRef.current.clearOverlays();
        addPoints(points);
      }
    }, [points]);

    useImperativeHandle(ref, () => {
      return {
        toggleFullscreen,
        isFullscreen,
        chargeCenterByName: chargeCenterByName,
        chargeCenterByPoi: chargeCenterByPoi,
      };
    });
    return (
      <div ref={containerRef} className="map-container w-full h-full">
        {children}
      </div>
    );
  }),
);
