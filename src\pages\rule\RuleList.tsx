import { userApi } from '@/api/user';
import { XcModalForm } from '@/components/XcModal/XcModalForm';
import { IColumns } from '@/components/XcTable/interface/search.interface';
import { request, useRequest } from '@umijs/max';
import { Button, message, Switch, Typography } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import AddOrEdit from './components/AddOrEdit';
import { IconAdd } from '@/assets/svg';
import { sleep } from '@/utils/common.util';
import _ from 'lodash';
import { ITableRef, XcTableNew } from '@/components/XcTable/XcTableNew';
import RuleLevel from './components/RuleLevel';
import { ewsAPI } from '@/api/ews';
import { permissionApi } from '@/api/permission';
import { DATE_FMT } from '@/utils/time.util';
import PermissionWrap from '@/components/PermissionWrap';
import { STATIC_PERMISSION_CODE } from '@/utils/permission';

const { Text } = Typography;

enum MODAL_TYPE {
  none = 0,
  add = 1,
  edit = 2,
  status = 4, // 更改状态
  delete = 5, // 删除
}

export default () => {
  const tableRef = useRef<ITableRef>(null);
  const [modalType, setModalType] = useState<MODAL_TYPE>(MODAL_TYPE.none);
  const [currentItem, setCurrentItem] = useState<Record<string, any>>({});
  const [ruleList, setRuleList] = useState<any>([]);
  /**
   * 用户列表请求接口
   */
  const userListRequest = useRequest(
    (data?: any, headers?: any) => ({
      url: ewsAPI.getRuleList,
      params: { ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: { total: 0, page: 1, pageSize: 50, list: [] } },
      throwOnError: true,
    },
  );

  // 刷新数据
  const queryTableData = async () => {
    try {
      await tableRef.current?.onSubmit();
    } catch (error) {
      console.error('🚀 ~ list~ error:', error);
    }
  };

  /**
   * 更新用户请求接口
   */
  const userEditRequest = useRequest(
    (id, data?: any, headers?: any) => ({
      method: 'PUT',
      url: ewsAPI.putRule(id),
      data: { ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: null },
      throwOnError: true,
    },
  );

  const requestTable = async (params: any) => {
    const { current, pageSize, ...others } = params;
    const payload: Record<string, any> = {
      page: current,
      page_size: pageSize,
    };
    if (others.status === '99') {
      delete others.status;
    }
    if (others.main_type === '99') {
      delete others.main_type;
    }
    if (others.level === '99') {
      delete others.level;
    }

    const queryPayload = {
      ...others,
      ...payload,
      tenant_id: 1,
    };
    const result = await userListRequest.run(queryPayload);

    if (result.code === 200) {
      return {
        total: result.data.total || 0,
        data: result.data.list || [],
      };
    } else {
      message.error(result.message || '请求失败');
    }

    return {
      total: 0,
      data: [],
    };
  };
  const showModal = async (item: any, type: MODAL_TYPE) => {
    setCurrentItem(item);
    setModalType(type);
  };
  const closeModal = () => {
    setModalType(MODAL_TYPE.none);
  };
  const toggleModalVisible = (open: boolean) => {
    if (!open) {
      closeModal();
    }
  };
  // 用户状态 --start
  const onStatusChange = async (data: any) => {
    const status = data.status === 1 ? 2 : 1;
    return userEditRequest
      .run(data.id, {
        ...data,
        status,
      })
      .then((res) => {
        if (res.code === 200) {
          const text = status === 1 ? '用户已启用。' : '用户已停用。';
          message.info(res.message || text);
          closeModal();
          queryTableData();
          return true;
        } else {
          message.error(res.message || '修改失败，请重试。');
          return false;
        }
      });
  };
  const showStatusModal = async (item: any) => {
    request(ewsAPI.getRule(item.id), {
      params: { tenant_id: 1 },
    }).then((res) => {
      if (res.code === 200) {
        if (item.status === 1) {
          // showModal(res.data, MODAL_TYPE.status);
          onStatusChange(res.data);
        } else {
          onStatusChange(res.data);
        }
      } else {
        message.error(item.status === 2 ? '启用失败，请重试。' : '停用失败，请重试。');
      }
    });
  };
  const onDelete = async (item: any) => {
    return request(ewsAPI.deleteRule(item.id), {
      method: 'DELETE',
      data: {
        tenant_id: 1,
      },
    }).then(async (res) => {
      if (res.code === 200) {
        message.info(res.message || '删除成功');
        queryTableData();
        return true;
      } else {
        message.error(res.message || '删除失败');
        return true;
      }
    });
  };

  // 修改--start
  const showEditModal = (item: any) => {
    request(ewsAPI.getRule(item.id), {
      params: { tenant_id: 1 },
    }).then(async (res) => {
      setCurrentItem(res.data);
      await sleep(100);
      showModal(res.data, MODAL_TYPE.edit);
    });
  };
  useEffect(() => {
    request(permissionApi.getDataPermissionList, {
      method: 'GET',
      params: {
        tenant_id: 1,
        type: 'sub_type',
      },
    }).then((res: any) => {
      const list = res?.data?.list?.[0].children || [];
      setRuleList(list);
    });
  }, []);
  // 修改--end
  const columns: IColumns[] = [
    {
      title: '预警类型',
      width: 150,
      key: 'main_type',
      hideInTable: true,
      initialValue: '99',
      valueType: 'drop',
      realtime: true,
      valueEnum: [
        { label: '全部预警类型', value: '99' },
        ...ruleList.map((item: any) => {
          return {
            label: item.name,
            value: item.code,
          };
        }),
      ],
    },
    {
      title: '预警等级',
      width: 150,
      key: 'level',
      hideInTable: true,
      initialValue: '99',
      valueType: 'drop',
      realtime: true,
      valueEnum: [
        {
          label: '全部预警等级',
          value: '99',
        },
        {
          label: '高等',
          value: 'high',
        },
        {
          label: '中等',
          value: 'medium',
        },
        {
          label: '低等',
          value: 'low',
        },
      ],
    },
    {
      title: '全部状态',
      width: 120,
      key: 'status',
      hideInTable: true,
      initialValue: '99',
      valueType: 'drop',
      realtime: true,
      valueEnum: [
        { label: '全部状态', value: '99' },
        { label: '启用', value: '1' },
        { label: '停用', value: '2' },
      ],
    },
    {
      title: '规则名称',
      width: 320,
      key: 'code',
      hideInTable: true,
      placeholder: '搜索规则编号/规则名称',
    },
    {
      title: '序号',
      dataIndex: 'id',
      key: 'id',
      link: true,
      hideInSearch: true,
      render: (_, item: any) => {
        return (
          <Text
            className="text-txt-main font-normal hover:text-txt-blue  cursor-pointer"
            ellipsis={{ tooltip: item.id }}
            onClick={() => {
              showEditModal(item);
            }}
          >
            {item.id}
          </Text>
        );
      },
    },
    {
      title: '规则名称',
      dataIndex: 'name',
      key: 'name',
      hideInSearch: true,
      render: (value) => {
        return (
          <Text className="text-txt-main " ellipsis={{ tooltip: value }}>
            {value}
          </Text>
        );
      },
    },
    {
      title: '预警类型',
      dataIndex: 'main_type',
      key: 'main_type',
      hideInSearch: true,
      render: (value) => {
        return (
          <Text className="text-txt-main " ellipsis={{ tooltip: value }}>
            {value}
          </Text>
        );
      },
    },
    {
      title: '触发条件',
      dataIndex: 'condition',
      key: 'condition',
      hideInSearch: true,
      render: (value) => {
        return (
          <Text className="text-txt-main line-clamp-2" ellipsis={{ tooltip: value }}>
            {value}
          </Text>
        );
      },
    },
    {
      title: '预警等级',
      dataIndex: 'level',
      key: 'level',
      hideInSearch: true,
      render: (value) => {
        return <RuleLevel value={value} />;
      },
    },
    {
      title: '预警文案',
      dataIndex: 'message',
      key: 'message',
      hideInSearch: true,
      render: (value) => {
        return (
          <Text className="text-txt-main " ellipsis={{ tooltip: value }}>
            {value}
          </Text>
        );
      },
    },

    {
      title: '启用状态',
      dataIndex: 'status',
      key: 'status',
      hideInSearch: true,
      render: (_, item: { status: number }) => {
        return (
          <PermissionWrap accessCode={STATIC_PERMISSION_CODE.修改预警规则} useDisabled>
            <Switch onClick={() => showStatusModal(item)} checked={item.status === 1} />
          </PermissionWrap>
        );
      },
    },
    {
      title: '创建人',
      dataIndex: 'creator_name',
      key: 'creator_name',
      hideInSearch: true,
      render: (value) => {
        return (
          <Text className="text-txt-main " ellipsis={{ tooltip: value }}>
            {value}
          </Text>
        );
      },
    },
    {
      title: '创建类型',
      dataIndex: 'creator_name',
      key: 'creator_name',
      hideInSearch: true,
      render: (value) => {
        if (value === 'admin') {
          return '系统创建';
        }
        return '用户创建';
      },
    },
    {
      title: '更新时间',
      dataIndex: 'updated_at',
      key: 'updated_at',
      hideInSearch: true,
      render: (value) => {
        const txt = dayjs(value).format(DATE_FMT.DATE_TIME);
        return (
          <Text className="text-txt-main" ellipsis={{ tooltip: txt }}>
            {txt}
          </Text>
        );
      },
    },
    {
      title: '操作',
      align: 'center',
      fixed: 'right',
      width: 110,
      className: 'flex items-center justify-center',
      hideInSearch: true,
      render: (_, item: any) => {
        return (
          <div className={'flex items-center justify-end gap-[16px]'}>
            <PermissionWrap accessCode={STATIC_PERMISSION_CODE.修改预警规则}>
              <Button
                type="link"
                className="text-txt-main px-[0px]"
                onClick={() => showEditModal(item)}
              >
                编辑
              </Button>
            </PermissionWrap>
            <PermissionWrap accessCode={STATIC_PERMISSION_CODE.删除预警规则}>
              <Button
                type="link"
                className="text-txt-main px-[0px] hover:text-txt-blue"
                onClick={() => showModal(item, MODAL_TYPE.delete)}
              >
                删除
              </Button>
            </PermissionWrap>
          </div>
        );
      },
    },
  ];

  return (
    <div className="flex-1 flex">
      <XcTableNew
        ref={tableRef}
        loading={userListRequest.loading}
        columns={columns}
        request={requestTable}
        extend={null}
        batchRender={() => null}
        operator={
          <>
            <PermissionWrap accessCode={STATIC_PERMISSION_CODE.创建预警规则}>
              <Button
                type="primary"
                className={
                  'w-[124px] h-[40px] rounded-[8px] bg-btn-blue text-txt-white text-[14px]'
                }
                icon={<IconAdd className="!align-middle" />}
                onClick={() => {
                  showModal({}, MODAL_TYPE.add);
                }}
              >
                <span className={'!self-center'}>新增规则</span>
              </Button>
            </PermissionWrap>
          </>
        }
        rowSelection={null}
      />
      {modalType === MODAL_TYPE.edit || modalType === MODAL_TYPE.add ? (
        <AddOrEdit
          modalType={modalType}
          onClose={closeModal}
          visible={true}
          queryTableData={queryTableData}
          currentItem={currentItem}
          ruleList={ruleList}
        ></AddOrEdit>
      ) : null}
      <XcModalForm
        key="确认"
        title={'禁用'}
        width={500}
        open={modalType === MODAL_TYPE.status}
        modalProps={{
          zIndex: 1000,
          destroyOnClose: true,
          centered: true,
        }}
        onOpenChange={toggleModalVisible}
        onFinish={() => onStatusChange(currentItem)}
      >
        <div className="my-[40px]">
          您确定要停用该规则吗? 停用后，该规则无法对设备起预警作用，请谨慎操作。
        </div>
      </XcModalForm>
      <XcModalForm
        key="删除"
        title={'删除'}
        width={500}
        open={modalType === MODAL_TYPE.delete}
        modalProps={{
          zIndex: 1000,
          destroyOnClose: true,
          centered: true,
        }}
        onOpenChange={toggleModalVisible}
        onFinish={() => onDelete(currentItem)}
      >
        <div className="my-[40px]">
          您确定要删除该规则吗? 删除后，该规则无法对设备起预警作用，请谨慎操作。
        </div>
      </XcModalForm>
    </div>
  );
};
