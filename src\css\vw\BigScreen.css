.BigScreen {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(17, 39, 81);
  overflow: hidden;
  z-index: 9;
}


.header {
  position: relative;
  display: flex;
  color: #fff;
  font-size: 14px;
  align-items: center;
  justify-content: center;
  height: 58px;
  z-index: 999;
}

.headImg {
  position: absolute;
  width: 100%;
  left: 50%;
  top: 0;
  height: 78px;
  transform: translateX(-50%);
  display: flex;
  justify-content: center;
  background-size: 1920px 78px;
  background-position: center center;
}

.headTitle {
  margin-top: 10px;
  width: 637px;
  height: 55px;
}

.headerLeft {
  position: absolute;
  display: flex;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  align-items: center;
  gap: 16px;
  border-radius: 8px;
}

.headerSettings {
  display: flex;
  color: #fff;
  gap: 4px;
  display: none;
}

.homeIcon {
  width: 30px;
  height: 30px;
  cursor: pointer;
}


.headerIcon {
  width: 22px;
  height: 22px;
}

.mainContent {
  position: relative;
  height: calc(100% - 58px);
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  overflow: hidden;
}

.leftContainer {
  position: absolute;
  left: 20px;
  top: 0;
  padding-top: 12px;
  bottom: 0;
  z-index: 99;
  width: 450px;
}


.leftContainerCont {
  margin-top: 14px;
  margin-bottom: 20px;
  border-radius: 16px;
  padding: 24px 0px 30px;
  background: rgba(17, 19, 41, 0.99);
  box-sizing: border-box;
}

.rightContainer {
  position: absolute;
  z-index: 99;
  right: 20px;
  top: 0;
  padding-top: 52px;
  bottom: 0px;
  display: flex;
  gap: 16px;
  flex-direction: column;
}

.baiduMapContainer {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

.todayDataTitle {
  color: #fff;
  padding-left: 30px;
  font-size: 18px;
  font-weight: 500;
  line-height: 25px;
  margin-bottom: 24px;
}

.BigScreen-2dBtn {
  margin-bottom: 12px;
  background: rgba(17, 19, 41, 0.99);;
  color: #fff;
  height: 40px;
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 8px;
}
