import areaData from '@/constants/area-guangdong.json';
import { isBlank } from '@/utils/string.util';
import { ProForm, ProFormDependency, ProFormSelect } from '@ant-design/pro-components';
// 省列表
const provinceList = Object.keys(areaData);

interface IProps {
  formRef: any;
  label?: React.ReactNode;
}

export const XcAddress = (props: IProps) => {
  const { formRef, label } = props;
  return (
    <ProForm.Group key="group" size={10}>
      <ProFormSelect
        options={provinceList.map((item) => ({
          value: item,
          label: item,
        }))}
        required={true}
        rules={[
          {
            required: true,
            message: '请选择',
          },
        ]}
        placeholder={'省(自治区、直辖市)'}
        width={100}
        name="province"
        label={label ? label : <span className="text-[12px] text-txt-sub">客户地址</span>}
      />
      <ProFormDependency name={['province', 'city']}>
        {({ province, city: prvCity }) => {
          const cityList = province ? Object.keys((areaData as any)[province] || {}) : [];

          if (!isBlank(prvCity) && !cityList.includes(prvCity)) {
            formRef.current?.resetFields(['city']);
          }

          return (
            <ProFormSelect
              options={cityList.map((item) => ({
                value: item,
                label: item,
              }))}
              width={100}
              placeholder={'市(地区)'}
              required={false}
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
              name="city"
              label={<span className="text-[12px] text-txt-sub hidden">城市</span>}
            />
          );
        }}
      </ProFormDependency>
      <ProFormDependency name={['province', 'city', 'district']}>
        {({ city, province, district: prvDistrict }) => {
          const districtList = city ? Object.keys((areaData as any)[province][city]) : [];

          if (!isBlank(prvDistrict) && !districtList.includes(prvDistrict)) {
            formRef.current?.resetFields(['district']);
          }
          return (
            <ProFormSelect
              options={districtList.map((item) => ({
                value: item,
                label: item,
              }))}
              placeholder={'区(镇)'}
              required={false}
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
              width={100}
              name="district"
              label={<span className="text-[12px] text-txt-sub hidden">区县</span>}
            />
          );
        }}
      </ProFormDependency>
      <ProFormDependency name={['province', 'city', 'district', 'street']}>
        {({ city, province, district, street: prvStreet }) => {
          const streetList = district ? (areaData as any)[province][city][district] : [];

          if (!isBlank(prvStreet) && !streetList.includes(prvStreet)) {
            formRef.current?.resetFields(['street']);
          }
          return (
            <ProFormSelect
              options={streetList.map((item: string) => ({
                value: item,
                label: item,
              }))}
              placeholder={'街道(乡)'}
              required={false}
              rules={[
                {
                  required: true,
                  message: '请选择',
                },
              ]}
              width={100}
              name="street"
              label={<span className="text-[12px] text-txt-sub hidden">街道</span>}
            />
          );
        }}
      </ProFormDependency>
    </ProForm.Group>
  );
};
