import React, { useEffect, useMemo } from 'react';
import Panel from '@/components/Panel';
import EChartsCommon from '@/components/Charts/Charts/EChartsCommon';
import dayjs from 'dayjs';
import { statisticsAPI } from '@/api';
import { useRequest, history } from '@umijs/max';
import { DATE_FMT } from '@/utils/time.util';
import { getMosColor } from '@/utils/mosqutio.util';
import { Spin } from 'antd';

const GrowthRatePanel: React.FC<{
  startTime: string;
  endTime: string;
  range: any;
  mosquitoType: string;
}> = (props) => {
  const { startTime, endTime, range, mosquitoType } = props;
  const {
    run: getGrowthRateReq,
    data: growthRateRes,
    loading,
  } = useRequest(
    (data?: any, headers?: any) => ({
      url: statisticsAPI.getGrowthRate,
      method: 'GET',
      params: { ...data, tenant_id: 1 },
      headers,
    }),
    {
      manual: true,
      throwOnError: true,
    },
  );

  useEffect(() => {
    if (typeof range?.province === 'undefined') return;
    getGrowthRateReq({
      growth_type: 'mtom',
      // start_time: dayjs().subtract(12, 'month').format(DATE_FMT.DATE_TIME),
      // end_time: dayjs().format(DATE_FMT.DATE_TIME),
      start_time: startTime,
      end_time: endTime,
      time_unit: 'day',
      ...range,
      monitor_type: mosquitoType,
    });
  }, [range, mosquitoType]);

  const option = useMemo(() => {
    let result = growthRateRes?.type_groups?.[0]?.trend_points || [];

    return {
      tooltip: { trigger: 'axis' },
      legend: { top: 10, right: 20 },
      grid: { left: 40, right: 20, top: 40, bottom: 60 },
      xAxis: [
        {
          type: 'category',
          data: result.map((item: any) => dayjs(item.start_time).format(DATE_FMT.DAY)),
          axisLine: { lineStyle: { color: '#E4E6EB' } },
          axisLabel: { color: '#797A85', rotate: 45 },
        },
      ],
      yAxis: [
        {
          type: 'value',
          splitLine: { lineStyle: { color: '#E4E6EB', type: 'dashed' } },
        },
      ],
      series: [
        {
          name: '总蚊虫环比增长率',
          type: 'line',
          itemStyle: { color: getMosColor('其他') },
          symbol: 'emptyCircle',
          symbolSize: 6,
          data: result.map((s: any) => s.growth_rate + 0),
          emphasis: { focus: 'series' },
          label: {
            show: true,
            position: 'center',
          },
        },
      ],
    };
  }, [growthRateRes]);
  const gotoAnalysis = () => {
    history.push('/data/analysis/growth');
  };
  return (
    <Panel
      title={
        <span className="flex items-center gap-2">
          <span>蚊虫环比增长率</span>
          <div className="text-[12px] text-[#999]">
            （该模块展示的是当天监测的蚊虫总数与上一天监测总数增长率变化趋势）
          </div>
        </span>
      }
      actions={
        <div onClick={gotoAnalysis} className=" cursor-pointer">
          <svg width="20" height="20" fill="none">
            <circle cx="4" cy="10" r="2" fill="#797A85" />
            <circle cx="10" cy="10" r="2" fill="#797A85" />
            <circle cx="16" cy="10" r="2" fill="#797A85" />
          </svg>
        </div>
      }
      contentStyle={{
        overflow: 'visible',
      }}
    >
      <div className="w-full h-[260px] flex items-center justify-center">
        {loading ? <Spin /> : <EChartsCommon option={option} />}
      </div>
    </Panel>
  );
};

export default GrowthRatePanel;
