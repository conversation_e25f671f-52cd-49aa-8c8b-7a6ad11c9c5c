import { Checkbox, Table, TableProps } from 'antd';
import { styled } from '@umijs/max';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import useResize from '@/hooks/useResize';
import _ from 'lodash';
import { getTreeProperty } from '@/utils/tree.util';
export const XcStyledTable = styled(Table)`
  .ant-table-row-selected,
  .ant-table-cell-row-hover {
    .ant-table-cell {
      background: rgba(54, 56, 58, 0.06) !important;
    }
  }

  .ant-table-row > .ant-table-cell {
    min-height: 44px !important;
    padding: 11px 16px !important;
    box-sizing: border-box !important;
    & > div,
    & > button,
    & > p,
    & > span {
      line-height: 22px;
      vertical-align: top;
    }
    button {
      max-height: 22px;
      &.ant-table-row-expand-icon {
        max-height: 22px;
      }
    }
  }

  .ant-table-container,
  .ant-table-header,
  .ant-table-header > table {
    /* border-radius: 0px !important; */
  }
  .ant-table-thead,
  .ant-table-header {
    border-top: 1px solid var(--line-split);
    border-bottom: 1px solid var(--line-split);

    .ant-table-cell {
      background-color: #e9f2fb;
      border: none;
      color: var(--txt-main) !important;
      border-radius: 0px !important;
      padding: 11px 16px !important;

      &::before {
        display: none;
      }
    }
  }

  .ant-table-measure-row {
    border-bottom-color: var(--line-split);
  }

  .ant-table-body {
    table {
      overflow: hidden;
    }
    scrollbar-color: auto;
    &::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
      /* 轨道背景透明 */
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb {
      background: #00000026;
      border-radius: 4px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #00000033;
    }

    td {
      border-bottom: 1px solid var(--line-split) !important;
    }
  }
  .ant-table-tbody-virtual-scrollbar-thumb {
    background: #00000026 !important;
    border-radius: 4px !important;
  }
  .ant-select-selector,
  .ant-pagination-prev,
  .ant-pagination-next,
  .ant-pagination-item {
    border: 1px solid var(--line-split);
    border-radius: 4px;
    color: var(--txt-main);
    box-sizing: border-box;
  }
  .ant-pagination-item-active {
    > a {
      color: var(--txt-main) !important;
    }
    background-color: color-mix(in srgb, var(--txt-third) 15%, transparent);
  }
  .ant-pagination-options {
    height: 30px;
  }
  .ant-select-item-option-content {
    color: var(--txt-main);
  }
  .ant-pagination-total-text {
    flex: 1;
  }
  .ant-checkbox-input {
    border: 1px solid var(--line-split);
  }
  .ant-table-selection-column + .ant-table-cell {
    padding-left: 0 !important;
  }
  .ant-table-cell-row-hover {
    .ant-btn-link,
    .cursor-pointer {
      color: var(--txt-blue);
      &:hover {
        font-weight: 600;
      }
    }
  }
`;
export type IProps = {
  headHeight?: number;
  bottomMargin?: number;
  paginationHeight?: number;
  rowSelection?: any;
  rowKey?: string;
};
export default function XcAntdTable(props: IProps & TableProps) {
  const {
    headHeight = 46,
    paginationHeight = 32,
    bottomMargin = 16,
    rowSelection,
    pagination,
    ...others
  } = props;
  const { onChange: onRowChange, selectedRowKeys: selectedKeys, ...rowOthers } = rowSelection || {};
  const [scroll, setScroll] = useState<any>({ x: 'max-content', y: 'max-content' });
  const boxRef = useRef<HTMLDivElement>(null);
  const resize = useCallback(() => {
    const height = boxRef.current?.clientHeight || 0;
    const width = boxRef.current?.clientWidth || 0;
    const th = height - headHeight - paginationHeight - bottomMargin * 2;
    const tw = width - 17;
    setScroll({
      x: tw || 'max-content',
      y: th,
    });
  }, []);
  // const [expandKeys, setExpandKeys] = useState();
  // const onExpandedRowsChange = (expandedRowsKeys: any) => {
  //   setExpandKeys(expandedRowsKeys);
  // }
  useResize(resize, []);

  // 存储全选状态
  const allKeys: any = useMemo(() => {
    // 如果没有id, 要手动给个id才行
    return getTreeProperty(props.dataSource, props.rowKey || 'id');
  }, [props.dataSource]);

  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [checkAllState, setCheckAllState] = useState({
    checked: false,
    indeterminate: false,
  });
  const checkedRow = useCallback(
    (newSelectedRowKeys: any) => {
      const isNoneChecked = newSelectedRowKeys.length === 0;
      const isAllChecked = newSelectedRowKeys.length === allKeys.length && !isNoneChecked;
      const isIndeterminate = !isAllChecked && !isNoneChecked;
      setSelectedRowKeys(newSelectedRowKeys);
      setCheckAllState({
        checked: isAllChecked,
        indeterminate: isIndeterminate,
      });
    },
    [allKeys],
  );
  // 行选择改变时的回调函数
  const onSelectChange = useCallback(
    (newSelectedRowKeys: any) => {
      checkedRow(newSelectedRowKeys);
      onRowChange?.(newSelectedRowKeys);
    },
    [onRowChange, checkedRow],
  );

  useEffect(() => {
    checkedRow(selectedKeys || []);
  }, [selectedKeys]);

  // 全选框改变状态时的回调函数
  const handleSelectAll = useCallback(
    (e: any) => {
      const checked = e.target.checked;
      if (checked) {
        // 若全选框被选中，选中所有行
        onSelectChange(allKeys);
      } else {
        // 若全选框取消选中，清空选中行
        onSelectChange([]);
      }
    },
    [allKeys, onSelectChange],
  );
  const showTotal = useCallback(
    (total: number) => {
      return (
        <div className="flex flex-1 items-center pagination-total">
          {rowSelection ? (
            <div className="size-[16px] ml-[16px] flex items-center">
              <Checkbox
                checked={checkAllState.checked}
                indeterminate={checkAllState.indeterminate}
                onChange={handleSelectAll}
              />
            </div>
          ) : null}
          <span className={'ml-[16px] text-txt-main'}>共{total}条</span>
        </div>
      );
    },
    [checkAllState, handleSelectAll],
  );

  const tableOptions = useMemo(() => {
    const options = _.merge(
      {
        rowKey: props.rowKey || 'id',
        pagination:
          pagination === false
            ? false
            : {
                showQuickJumper: true,
                showTotal: showTotal,
                pageSizeOptions: ['2', '10', '20', '50', '100'],
                showSizeChanger: true,
                ...pagination,
              },
        rowSelection: rowSelection
          ? {
              columnWidth: 48,
              fixed: true,
              selectedRowKeys,
              onChange: onSelectChange,
              ...rowOthers,
            }
          : undefined,
      },
      others,
    );
    return options;
  }, [others, onSelectChange, rowSelection, selectedRowKeys, showTotal, pagination]);

  return (
    <div ref={boxRef} className="relative w-full h-full" data-name="boxRef">
      <div className="absolute h-full w-full xc-antd-table inset-0 overflow-hidden">
        <XcStyledTable
          scroll={scroll}
          virtual={tableOptions.virtual === false ? false : true}
          {...tableOptions}
          className="xc-antd-table-comp"
        />
      </div>
    </div>
  );
}
