import Panel from '@/components/Panel';
import { DownloadOutlined } from '@ant-design/icons';
import { Button, Spin, Tree } from 'antd';
import Table, { ColumnsType } from 'antd/es/table';
import { ReactComponent as IconRegionMos } from '../../assets/region-mos.svg';
import { useEffect, useRef } from 'react';
import { statisticsAPI } from '@/api';
import { styled, useModel, useRequest } from '@umijs/max';
import { DATE_FMT, getRangeByTimeUnitAndFormat, TIME_UNITS } from '@/utils/time.util';
import DataSearchForm from '@/pages/data/components/DataSearchForm';
import { useMemoizedFn } from 'ahooks';
interface DataType {
  key: string;
  rank: number;
  region: string;
  total_count: number;
  ratio: number;
}

const StyledTree = styled(Tree)`
  .ant-tree-treenode {
    width: 100%;
    line-height: 36px;
  }

  .ant-tree-treenode-selected,
  .ant-tree-treenode:hover {
    background-color: rgba(244, 244, 252);
    border-radius: 4px;
  }
  .ant-tree-node-content-wrapper {
    background-color: transparent !important;
  }
  .ant-tree-switcher:before {
    height: 100%;
  }
`;

const StyledTable = styled(Table)`
  .ant-table-content {
    .ant-table-thead,
    .ant-table-row {
      height: 36px !important;
    }
    .ant-table-cell {
      padding: 0 12px !important;
    }
    .ant-table-thead {
      .ant-table-cell {
        color: var(--txt-sub) !important;
      }
    }
  }
`;

export default function MosAreaData(props: any) {
  const { menu } = props;
  const { defaultRegionKey, defaultRegion, regionLoad } = useModel('useDevice', (m) => ({
    defaultRegionKey: m.defaultRegionKey,
    defaultRegion: m.defaultRegion,
    regionLoad: m.regionLoad,
  }));

  const defaultRange = getRangeByTimeUnitAndFormat(TIME_UNITS.MONTH, DATE_FMT.DATE_TIME);
  const queryRef = useRef<any>({
    start_time: defaultRange[0],
    end_time: defaultRange[1],
    ...(defaultRegion || {}),
  });
  const {
    run: getRegionRank,
    data: regionRankRes,
    loading,
  } = useRequest(
    (data?: any, headers?: any) => {
      return {
        url: statisticsAPI.getRegionRank,
        method: 'GET',
        params: { ...queryRef.current, ...data, tenant_id: 1 },
        headers,
      };
    },
    {
      manual: true,
      throwOnError: true,
    },
  );

  const columns: ColumnsType<DataType> = [
    {
      title: '排名',
      dataIndex: 'rank',
      key: 'rank',
      width: '25%',
    },
    {
      title: '监测区域',
      dataIndex: 'region',
      key: 'region',
      width: '25%',
    },
    {
      title: '蚊虫监测总数（只）',
      dataIndex: 'total_count',
      key: 'total_count',
      width: '25%',
    },
    {
      title: '该区域监测数占总蚊虫数量比例（%）',
      key: 'ratio',
      minWidth: 320,
      render: (_, record) => (
        <div className="flex items-center">
          <div className="w-full h-[8px] bg-gray-200 rounded-full mr-2">
            <div
              className="h-full bg-[#4CD787] rounded-full"
              style={{ width: `${record.ratio}%` }}
            />
          </div>
          <span className="flex-shrink-0 w-[80px]">{record.ratio}%</span>
        </div>
      ),
    },
  ];

  const getRegionRankData = useMemoizedFn((data?: any) => {
    const { monitor_type } = data || {};

    queryRef.current = {
      ...queryRef.current,
      ...defaultRegion,
      ...data,
      monitor_type: monitor_type || '',
    };

    getRegionRank();
  });

  const onTreeSelect = (keys: any) => {
    const str = (keys[0] || '').toString();
    const [province, city, district, street, device_id] = str.split('.');
    queryRef.current = Object.assign(queryRef.current, {
      province,
      city,
      district,
      street,
      dev_ids: device_id,
    });
    getRegionRankData();
    return false;
  };

  // 下载按钮作为 Panel 的 actions
  const downloadButton = (
    <Button type="primary" icon={<DownloadOutlined />} className="h-[32px] bg-[#1677FF] !hidden">
      下载
    </Button>
  );
  return (
    <Panel
      title={
        <span className="flex items-center gap-2">
          <IconRegionMos className="text-[#9F8CF7]"></IconRegionMos>
          <span>蚊虫高发区域排名</span>
          <div className="text-[12px] text-[#999]">（该模块是以蚊虫监测数量排名）</div>
        </span>
      }
      actions={downloadButton}
      contentStyle={{
        padding: '0',
      }}
      boxStyle={{ height: 635 }}
    >
      <div className="flex w-full h-full">
        {/* 左侧区域选择区域 - 使用百分比宽度 */}
        <div className="w-[274px] border-solid border-r-[1px] border-r-[#f0f0f0] h-full p-[12px] overflow-auto relative z-10">
          <div className="text-[14px] font-medium mb-[12px] leading-[22px]">区域选择</div>
          {menu?.length > 0 && regionLoad ? (
            <StyledTree
              fieldNames={{
                title: 'name',
                key: 'key',
                children: 'children',
              }}
              treeData={menu}
              className="h-[calc(100%-34px)] xc-scrollbar-y"
              checkStrictly={true}
              onSelect={onTreeSelect}
              defaultSelectedKeys={[defaultRegionKey]}
              multiple={false}
              defaultExpandAll
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <Spin />
            </div>
          )}
        </div>
        {/* 右侧自适应的内容区域 - 使用百分比宽度 */}
        <div className="flex-1 ml-[-1px] flex flex-col">
          {/* 表格头部筛选区域 */}
          <div className="flex items-center gap-[12px] p-[14px_12px] flex-shrink-0">
            <DataSearchForm
              menu={menu}
              onRequest={getRegionRankData}
              allowFields={['monitor_type', 'dateRange']}
            />
          </div>
          <div className="w-full flex-1  ">
            <StyledTable
              loading={loading}
              columns={columns as any}
              dataSource={regionRankRes?.regions || []}
              pagination={false}
              className="overflow-x-hidden"
              rowKey={'rank'}
            />
          </div>
        </div>
      </div>
    </Panel>
  );
}
