/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-02-12 19:03:23
 * @LastEditTime: 2025-02-12 22:01:47
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */

export function createXcPoint() {
  let div = document.createElement('div');
  div.style.zIndex = BMapGL.Overlay.getZIndex(this.point.lat) + '';
  div.style.height = this.properties.height + 'px';
  div.style.width = this.properties.width + 'px';
  div.style.display = 'flex';
  div.style.justifyContent = 'center';
  div.style.alignItems = 'center';
  div.style.flexDirection = 'column';
  div.className = 'circle-container';

  let img = document.createElement('img');
  img.style.width = this.properties.width + 'px';
  img.style.height = this.properties.height + 'px';
  img.src = this.properties.imgSrc;
  div.appendChild(img);

  div.onmouseover = function () {
    img.classList.add('jumping-marker');
  };

  div.onmouseout = function () {
    // 移除阴影
    img.classList.remove('jumping-marker');
  };

  div.onclick = this.properties.onClick;

  return div;
}
