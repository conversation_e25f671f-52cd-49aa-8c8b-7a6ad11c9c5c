import { isBlank } from '@/utils/string.util';
import { ConfigProvider } from 'antd';
import { forwardRef, memo, useImperativeHandle, useState } from 'react';
import { IconClose } from '../Icon/IconClose';
import { IconDrop } from '../Icon/IconDrop';
import { IconSearch } from '../Icon/IconSearch';
import { XcInput } from '../XcInput';
import { XcRangePicker } from '../XcRangePicker';
import { XcSelect } from '../XcSelect';
/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-23 23:40:05
 * @LastEditTime: 2025-02-13 15:46:31
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  columns?: IColumns[];
  children?: React.ReactNode;
  onSearch?: (criteria?: any) => void;
  onChange?: (criteria?: any) => void;
  className?: string;
}

export const XcTableSearch = memo(
  forwardRef((props: IProps, ref: any) => {
    const { columns, children, onSearch, onChange, className } = props;
    const [criteria, setCriteria] = useState<any>(() => {
      const defaultValues: Record<string, any> = {};
      columns?.forEach((item) => {
        if (item.key && !isBlank(item.key) && !isBlank(item.initialValue)) {
          defaultValues[item.key] = item.initialValue;
        }
      });

      return defaultValues;
    });

    const onSetSearch = (e: any, item: any) => {
      const payload = {
        ...criteria,
        [item.key]: e,
      };
      if (isBlank(e)) {
        delete payload[item.key];
      }
      setCriteria({ ...payload });
      if (onChange && item.realtime) {
        onChange({ ...payload });
      }
    };
    const onEventSearch = (e: any, item: any) => {
      const payload = {
        ...criteria,
        [item.key]: e,
      };
      if (isBlank(e)) {
        delete payload[item.key];
      }
      setCriteria({ ...payload });
      if (onChange) {
        onChange({ ...payload });
      }
    };

    const disabledDate = (current: any, info: any, rangeLimit: any) => {
      const { from } = info;
      if (rangeLimit) {
        const [time, unit] = rangeLimit;

        return Math.abs(current.diff(from, unit)) >= time;
      }
      return false;
    };

    useImperativeHandle(ref, () => {
      return {
        getCriteria: () => {
          return criteria;
        },
        onSubmit: () => {
          if (onSearch) {
            onSearch({ ...criteria });
          }
        },
      };
    });

    return (
      <ConfigProvider
        theme={{
          components: {
            Input: {
              colorBgContainer: '#F4F4F6',
              colorBorder: '#F4F4F6',
              activeBorderColor: '#E7E9ED',
              hoverBorderColor: '#E7E9ED',
            },
          },
        }}
      >
        <div className={'h-[80px] flex items-center justify-between ' + className}>
          <div className="h-[80px] flex gap-[10px] items-center">
            {columns?.map((item, index) => {
              const {
                valueType,
                valueEnum = {} as any,
                initialValue,
                maxValue,
                minValue,
                rangeLimit,
                placeholder,
                fieldProps,
                width,
                height,
                render,
              } = item;
              if (render) {
                return render(null, item, index, {
                  onChange: (data: any) => {
                    onSetSearch(data, item);
                  },
                });
              } else if (valueType === 'drop') {
                let options = [];
                if (Array.isArray(valueEnum)) {
                  options = valueEnum.map((item) => {
                    return {
                      label: item.name,
                      value: item.id,
                      ...item,
                    };
                  });
                } else {
                  options = Object.keys(valueEnum).map((key) => {
                    return { label: valueEnum[key], value: key };
                  });
                }

                return (
                  <XcSelect
                    key={index}
                    onSelect={(e) => {
                      onSetSearch(e, item);
                    }}
                    options={options}
                    defaultValue={initialValue}
                    placeholder={placeholder}
                  />
                );
              } else if (valueType === 'rangePicker' && item.key) {
                return (
                  <XcRangePicker
                    key={index}
                    defaultValue={initialValue}
                    value={criteria[item.key]}
                    maxDate={maxValue}
                    minDate={minValue}
                    disabledDate={(current, info) => disabledDate(current, info, rangeLimit)}
                    suffixIcon={<IconDrop className="size-[24px]" />}
                    onChange={(e) => {
                      onSetSearch(e, item);
                    }}
                    allowClear={{
                      clearIcon: (
                        <IconClose
                          onClick={() => {
                            setTimeout(() => {
                              onSetSearch(initialValue, item);
                            }, 0);
                          }}
                        />
                      ),
                    }}
                    {...fieldProps}
                  />
                );
              } else {
                if (item.key) {
                  return (
                    <div key={index} className="flex items-center">
                      <XcInput
                        value={criteria[item.key]}
                        allowClear={{
                          clearIcon: (
                            <IconClose
                              onClick={() => {
                                onEventSearch(initialValue, item);
                              }}
                            />
                          ),
                        }}
                        style={{
                          width: width || '320px',
                          height: height || '40px',
                          borderRadius: '8px',
                        }}
                        placeholder={placeholder}
                        prefix={
                          <IconSearch className="ml-[16px] mr-[8px] size-[20px] text-txt-sub " />
                        }
                        onPressEnter={(e: any) => onEventSearch(e.target.value, item)}
                        onBlur={(e: any) => onEventSearch(e.target.value, item)}
                        onChange={(e: any) => onSetSearch(e.target.value, item)}
                      />
                    </div>
                  );
                }
                return null;
              }
            })}
          </div>
          <div className="flex items-center h-[80px] gap-[10px]">{children}</div>
        </div>
      </ConfigProvider>
    );
  }),
);
