/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-30 00:26:28
 * @LastEditTime: 2025-02-08 00:24:28
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */

export interface IColumns {
  title: string;
  key?: string;
  width?: number;
  fixed?: string;
  className?: string;
  height?: number;
  align?: 'left' | 'right' | 'center';
  valueType?: 'rangePicker' | 'drop' | 'input';
  valueEnum?:
    | {
        [key: string]:
          | React.ReactNode
          | {
              text: React.ReactNode;
              status: 'Success' | 'Error' | 'Processing' | 'Warning' | 'Default';
            };
      }
    | Array<any>;
  initialValue?: any;
  maxValue?: any;
  minValue?: any;
  rangeLimit?: [number, string];
  placeholder?: any;
  link?: boolean;
  fieldProps?: any;
  dataIndex?: string;
  hideInTable?: boolean;
  hideInSearch?: boolean;
  realtime?: boolean;
  renderType?: COLUMN_RENDER_TYPE;
  render?: (
    text: any,
    record: any,
    index: number,
    action: any,
  ) => React.ReactNode | React.ReactNode[];
}
export enum COLUMN_RENDER_TYPE {
  text_ellipsis,
  text_position,
}
export interface SearchRef {
  getCriteria: () => any;
  onSubmit: () => void;
}
