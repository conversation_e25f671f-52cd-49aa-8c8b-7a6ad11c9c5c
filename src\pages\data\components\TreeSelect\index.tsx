import { px2font, px2vh } from '@/components/LargeScreen/vw/vw';
import { XcMenu2 } from '@/components/XcMenu/XcMenu2';
import { styled } from '@umijs/max';
import { ConfigProvider, TreeSelect } from 'antd';
import { useRef } from 'react';

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-22 01:57:53
 * @LastEditTime: 2025-02-13 12:47:55
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
const StyledTreeSelect = styled(TreeSelect)`
  height: ${() => px2vh(32)};
  > .ant-select-selector {
    border: 1px solid rgba(255, 255, 255, 0.18) !important;
    background: rgba(169, 169, 169, 0.08) !important;
  }
  .ant-select-selection-item {
    font-size: ${() => px2font(14)};
    color: #fff !important;
  }
  .ant-select-selection-placeholder {
    font-size: ${() => px2font(14)};
  }
`;

export const XcTreeSelect = (props: any) => {
  const {
    placeholder,
    suffixIcon,
    value,
    onClickItem,
    defaultOpenKeys,
    defaultSelectedKey,
    treeData,
  } = props;
  const selectRef = useRef<any>(null);

  return (
    <ConfigProvider
      theme={{
        components: {
          TreeSelect: {
            borderRadius: 6,
          },
        },
      }}
    >
      <StyledTreeSelect
        style={{ width: '100%' }}
        value={value}
        listHeight={350}
        dropdownStyle={{
          overflow: 'auto',
          background: '#1F1F34',
        }}
        ref={selectRef}
        dropdownRender={() => (
          <XcMenu2
            items={treeData}
            defaultExpandedKeys={defaultOpenKeys}
            onClickItem={(args) => {
              onClickItem(args);
              selectRef?.current?.blur();
            }}
            defaultSelectedKey={defaultSelectedKey}
          />
        )}
        placeholder={placeholder}
        treeDefaultExpandAll
        suffixIcon={suffixIcon}
      />
    </ConfigProvider>
  );
};
