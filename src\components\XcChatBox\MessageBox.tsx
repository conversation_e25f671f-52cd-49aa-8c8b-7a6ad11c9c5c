/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-17 19:48:31
 * @LastEditTime: 2025-02-09 16:10:59
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { DEFAULT_IMAGE } from '@/constants/common';
import { isBaseImg } from '@/utils/file.util';
import { Image, Spin } from 'antd';
import dayjs from 'dayjs';
import {
  JSXElementConstructor,
  Key,
  memo,
  ReactElement,
  ReactNode,
  useCallback,
  useMemo,
  useState,
} from 'react';
import Markdown from 'react-markdown';
import imgAvatar from './assets/img_avatar.png';

interface IProps {
  messageList: any[];
  uniqueId: string;
}

interface IMsgImage {
  src?: string;
}

const MsgImage = (props: IMsgImage) => {
  const { src } = props;
  const [preview, setPreview] = useState(true);
  return (
    <Image
      width={256}
      height={154}
      src={src}
      preview={preview}
      style={{ borderRadius: 16, objectFit: 'cover' }}
      onError={() => {
        setPreview(false);
      }}
      fallback={DEFAULT_IMAGE}
    />
  );
};
const genImg = ({ src }: any) => {
  return <MsgImage src={src} />;
};

export const MessageBox = memo((props: IProps) => {
  const { messageList, uniqueId } = props;
  const currentTime = useMemo(() => dayjs().format('今天HH:mm'), []);

  const isBase64Img = useCallback((str: string) => {
    const isbase64 = isBaseImg(str);
    return isbase64 ? (
      <Image src={str} width={256} height={154} style={{ borderRadius: 16, objectFit: 'cover' }} />
    ) : (
      <Markdown
        components={{
          p: 'div',
          img: genImg,
        }}
      >
        {str}
      </Markdown>
    );
  }, []);

  return (
    <section
      id={'msg' + uniqueId}
      className={`mt-[50px] px-[30px] flex-1 flex flex-col overflow-y-auto scroll-smooth xc-scrollbar text-txt-main  text-[20px]`}
    >
      <p className="w-full text-center text-[14px] font-normal">{currentTime}</p>
      {messageList?.map(
        (
          message: {
            person: string;
            nickname:
              | string
              | number
              | boolean
              | ReactElement<any, string | JSXElementConstructor<any>>
              | Iterable<ReactNode>
              | null
              | undefined;
            text: string;
          },
          index: Key | null | undefined,
        ) => {
          return (
            <div key={index} className="relative flex flex-col gap-[10px] mb-[30px]">
              {message.person === 'secondary' ? (
                // 星小尘
                <div className="flex flex-col w-full">
                  {/* 人物头像、名称 */}
                  <p className="mb-[10px] text-[14px]">{message.nickname}</p>
                  <div className="flex gap-[10px] w-full">
                    <img src={imgAvatar} className="w-[38px] h-[38px]" />
                    <div
                      className={`fade-in bg-[#F5F7FA] p-[10px] w-max rounded-[8px] clear-both text-txt-main text-[14px]`}
                    >
                      {message.text === '...' ? <Spin /> : isBase64Img(message.text)}
                    </div>
                  </div>
                </div>
              ) : (
                // 管理员
                <div className="flex flex-col w-full ">
                  <p className={'ml-auto mb-[10px] text-[14px]'}>{message.nickname}</p>
                  <div
                    className={`${
                      isBaseImg(message.text)
                        ? 'rounded-[16px] w-max ml-auto'
                        : 'bg-[#0080FF] rounded-[8px] p-[10px] w-max max-w-[520px] ml-auto text-white text-[14px]'
                    }`}
                  >
                    {message.text === '...' ? <Spin /> : isBase64Img(message.text)}
                  </div>
                </div>
              )}
            </div>
          );
        },
      )}
    </section>
  );
});
