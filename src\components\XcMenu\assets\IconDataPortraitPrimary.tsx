/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-10 23:24:08
 * @LastEditTime: 2025-01-14 22:30:06
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
const IconDataPortraitPrimary = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      {...rest}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M17.8 4H6.2C4.43269 4 3 5.43269 3 7.2V15.3C3 17.0673 4.43269 18.5 6.2 18.5H8L11.9253 14.0841C11.965 14.0393 12.035 14.0393 12.0747 14.0841L16 18.5H17.8C19.5673 18.5 21 17.0673 21 15.3V7.2C21 5.43269 19.5673 4 17.8 4Z"
        fill="#0080FF"
      />
      <path
        d="M15.3489 19.9374C15.6111 20.2647 15.3781 20.75 14.9587 20.75H9.03931C8.62021 20.75 8.38707 20.2653 8.64866 19.9379L11.6043 16.2383C11.8043 15.988 12.1849 15.9877 12.3852 16.2378L15.3489 19.9374Z"
        fill="#0080FF"
      />
      <path d="M15 10L15 14" stroke="white" strokeWidth="1.5" strokeLinecap="round" />
      <path d="M12 8L12 13" stroke="white" strokeWidth="1.5" strokeLinecap="round" />
      <path d="M9 11L9 14" stroke="white" strokeWidth="1.5" strokeLinecap="round" />
    </svg>
  );
};

export default IconDataPortraitPrimary;
