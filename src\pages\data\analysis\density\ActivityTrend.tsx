import React, { useMemo, useRef } from 'react';
import Panel from '@/components/Panel';
import EChartsCommon from '@/components/Charts/Charts/EChartsCommon';
import { ReactComponent as IconDensity } from '../assets/density.svg';
import { <PERSON><PERSON>, Spin } from 'antd';
import { statisticsAPI } from '@/api';
import { useRequest } from '@umijs/max';
import { EChartsOption } from 'echarts';
import DataSearchForm from '../../components/DataSearchForm';
import { getMosColor, getMosName } from '@/utils/mosqutio.util';

export default function ActivityTrend(props: any) {
  const { menu } = props;
  const queryRef = useRef<any>({});
  const {
    run: getDailyActivity,
    data: dailyActivityRes,
    loading,
  } = useRequest(
    (data?: any, headers?: any) => {
      queryRef.current = { ...data };
      return {
        url: statisticsAPI.getActivityTrend,
        method: 'get',
        params: {
          ...queryRef.current,
          monitor_type: queryRef.current?.monitor_type || '',
          tenant_id: 1,
        },
        headers,
      };
    },
    {
      manual: true,
    },
  );
  const dailyActivityData = useMemo(() => {
    const list = dailyActivityRes?.type_groups || [];
    let result = list.slice(0);
    return result;
  }, [dailyActivityRes]);

  const option = useMemo((): EChartsOption => {
    let result = dailyActivityData || [];
    result = result.map((item: any) => {
      return {
        ...item,
        color: getMosColor(item.monitor_type),
        name: item.monitor_type === 'all' ? '总蚊虫数量' : getMosName(item.monitor_type) || '其他',
      };
    });
    let xLabels = [];
    if (result?.[0]?.trend_points) {
      xLabels = result?.[0]?.trend_points.map((item: any) => {
        return item.time_point;
      });
    }
    const length = result?.[0]?.trend_points?.length;
    return {
      dataZoom:
        length > 32
          ? [
              {
                type: 'inside', // 滑动条类型
                show: false, // 显示滑动条
                xAxisIndex: [0], // 指定作用于 x 轴
                start: 0, // 初始显示的起始位置（百分比）
                end: 3200 / length, // 初始显示的结束位置（百分比）
                zoomLock: true,
              },
            ]
          : null,
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        show: true,
        icon: 'roundRect',
        data: result.map((item: any) => item.name),
        textStyle: {
          color: '#000',
        },
        left: 'center',
        bottom: 0,
      },
      grid: [
        {
          left: 30,
          right: 30,
          top: 40,
          bottom: 40,
          containLabel: true,
        },
      ],
      xAxis: [
        {
          type: 'category',
          data: xLabels,
          axisLabel: {
            rotate: 45,
          },
        },
      ],
      yAxis: [
        {
          type: 'value' as const,
          name: '蚊虫监测总数（只）',
          nameLocation: 'end',
          nameTextStyle: { color: '#797A85', align: 'left' },
        },
      ],
      series: result.map((item: any) => {
        return {
          name: item.name,
          type: 'line',
          areaStyle: {
            color: item.color,
          },
          itemStyle: {
            color: item.color,
          },
          label: {
            show: true,
            position: 'center',
          },
          symbolSize: 6,
          symbol: 'emptyCircle',
          data: item?.trend_points?.map((s: any) => s.activity + 0),
        };
      }),
    };
  }, [dailyActivityRes]);

  return (
    <Panel
      title={
        <span className="flex items-center gap-2">
          <IconDensity className="text-[#FC91AD]"></IconDensity>
          <span>蚊虫数量变化趋势</span>
          <div className="text-[12px] text-[#999]">（该模块展示的是蚊虫数量随时间的变化趋势）</div>
        </span>
      }
      actions={
        <Button type="primary" className="rounded-[6px] px-4 h-8 text-[14px] font-medium !hidden">
          下载
        </Button>
      }
      boxStyle={{ height: 488 }}
    >
      <DataSearchForm menu={menu} onRequest={getDailyActivity}></DataSearchForm>
      <div className="w-full h-[330px] flex items-center justify-center relative">
        <div className="w-full h-[300px]">
          {dailyActivityData?.length ? (
            <EChartsCommon option={option} />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              {loading ? <Spin /> : <span className="text-txt-sub">暂无数据</span>}
            </div>
          )}
        </div>
      </div>
    </Panel>
  );
}
