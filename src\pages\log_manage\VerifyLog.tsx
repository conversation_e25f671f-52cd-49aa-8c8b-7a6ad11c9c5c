import { COLUMN_RENDER_TYPE, IColumns } from '@/components/XcTable/interface/search.interface';
import { request, useRequest } from '@umijs/max';
import { Button, ConfigProvider, Image, message, Table, Typography } from 'antd';
import { useEffect, useRef, useState } from 'react';
import dayjs from 'dayjs';
import { DATE_FMT } from '@/utils/time.util';
import { logApi } from '@/api/log';
import { XcTableNew } from '@/components/XcTable/XcTableNew';

const { Text } = Typography;

export default () => {
  const [isPreviewVisible, setIsPreviewVisible] = useState(false);
  const [previewSrc, setPreviewSrc] = useState('');

  const logListRequest = useRequest(
    (data?: any, headers?: any) => ({
      url: logApi.getVerify,
      method: 'GET',
      params: { ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: { total: 0, page: 1, pageSize: 50, list: [] } },
      throwOnError: true,
    },
  );

  const requestTable = async (params: any) => {
    const { range_time, current, pageSize, ...others } = params;
    const payload: Record<string, any> = {
      page: current,
      page_size: pageSize,
    };
    if (others.menu === 'all') {
      delete others.menu;
    }
    if (others.action === 'all') {
      delete others.action;
    }
    if (range_time) {
      payload.start_time = dayjs(range_time[0]).startOf('day').format(DATE_FMT.DAY);
      payload.end_time = dayjs(range_time[1]).endOf('day').format(DATE_FMT.DAY);
    } else {
      // return;
    }
    const queryPayload = {
      ...others,
      ...payload,
      tenant_id: 1,
    };
    const result = await logListRequest.run(queryPayload);

    if (result.code === 200) {
      let list = result.data.list || [];
      // 给每一行添加一个唯一的key
      list = list.map((item: any, index: number) => {
        return {
          ...item,
          __rowKey: item.id + '_' + index,
        };
      });
      return {
        total: result.data.total || 0,
        data: list || [],
      };
    } else {
      message.error(result.message || '请求失败');
    }

    return {
      total: 0,
      data: [],
    };
  };

  const hidePicModal = () => {
    setPreviewSrc('');
    setIsPreviewVisible(false);
  };
  const showPicModal = (src: string) => {
    setIsPreviewVisible(true);
    setPreviewSrc(src);
  };
  const onVisibleChange = (value: boolean, prevValue: boolean): void => {
    if (!value) {
      hidePicModal();
    }
  };

  const columns: IColumns[] = [
    {
      title: '创建时间',
      key: 'range_time',
      initialValue: [dayjs().subtract(30, 'day'), dayjs()],
      maxValue: dayjs(),
      minValue: dayjs('2024-12-01'),
      rangeLimit: [18, 'month'],
      hideInTable: true,
      valueType: 'rangePicker',
    },
    {
      title: '设备编号',
      width: 320,
      key: 'code',
      hideInTable: true,
      placeholder: '搜索数据编号/设备名称/验证专家',
      valueType: 'input',
    },
    {
      title: '数据编号',
      dataIndex: 'id',
      key: 'id',
      hideInSearch: true,
    },
    {
      title: '设备名称',
      dataIndex: 'device_name',
      key: 'device_name',
      hideInSearch: true,
      renderType: COLUMN_RENDER_TYPE.text_ellipsis,
    },
    {
      title: '监测点位置',
      dataIndex: 'monitor_location',
      key: 'monitor_location',
      hideInSearch: true,
      width: 300,
      renderType: COLUMN_RENDER_TYPE.text_ellipsis,
    },
    {
      title: '生境类型',
      dataIndex: 'area_type',
      key: 'area_type',
      hideInSearch: true,
      renderType: COLUMN_RENDER_TYPE.text_ellipsis,
    },

    {
      title: '原始数据',
      dataIndex: 'original_data',
      key: 'original_data',
      hideInSearch: true,
      render: (value) => {
        return (value || '').split(',').map((txt: string, index: number) => {
          return <div key={index}>{txt}</div>;
        });
      },
    },
    {
      title: '原始蚊虫数量',
      dataIndex: 'original_count',
      key: 'original_count',
      hideInSearch: true,
      renderType: COLUMN_RENDER_TYPE.text_ellipsis,
    },
    {
      title: '验证后数据',
      dataIndex: 'audit_data',
      key: 'audit_data',
      hideInSearch: true,
      render: (value) => {
        return (value || '').split(',').map((txt: string, index: number) => {
          return <div key={index}>{txt}</div>;
        });
      },
    },
    {
      title: '验证后蚊虫数量',
      dataIndex: 'audit_count',
      key: 'audit_count',
      hideInSearch: true,
      renderType: COLUMN_RENDER_TYPE.text_ellipsis,
    },
    {
      title: '验证专家',
      dataIndex: 'auditor_name',
      key: 'auditor_name',
      hideInSearch: true,
      renderType: COLUMN_RENDER_TYPE.text_ellipsis,
    },
    {
      title: '验证次数',
      dataIndex: 'batch',
      key: 'batch',
      hideInSearch: true,
      renderType: COLUMN_RENDER_TYPE.text_ellipsis,
    },
    {
      title: '验证时间',
      dataIndex: 'audit_time',
      key: 'audit_time',
      hideInSearch: true,
      renderType: COLUMN_RENDER_TYPE.text_ellipsis,
    },
  ];
  const paginationRef = useRef({
    current: 1,
    defaultPageSize: 50,
    total: 0,
  });

  return (
    <div className="flex-1 flex ">
      <XcTableNew
        loading={logListRequest.loading}
        columns={columns}
        rowKey="__rowKey"
        request={requestTable}
        pagination={paginationRef.current}
        extend={null}
        batchRender={() => null}
        operator={null}
        rowSelection={null}
      />
      {isPreviewVisible ? (
        <Image
          src={previewSrc}
          style={{ width: '100%', display: 'none' }}
          preview={{
            onVisibleChange: onVisibleChange,
            toolbarRender: () => null,
            visible: true,
          }}
        />
      ) : null}
    </div>
  );
};
