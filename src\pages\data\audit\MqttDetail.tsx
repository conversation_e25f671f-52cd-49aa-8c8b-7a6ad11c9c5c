import { useEffect, useMemo, useRef, useState } from 'react';
import { Button, ConfigProvider, Drawer, message, Space, Tabs, Typography } from 'antd';
import { request, styled } from '@umijs/max';
import { MosquitoAPI } from '@/api';
import { IconEnvNew, IconImage, IconProcess, IconRecognition } from '@/assets/svg';
import { concatString } from '@/utils/string.util';
import { MOS_NAME_LIST_EN, MOS_ZH } from '@/constants/mosquitor';
import { XcDrawer } from '@/components/XcDrawer';
import RecognitionResult from './RecognitionResult';
import { ReactComponent as EnvImage } from './assets/env.svg';
import { ReactComponent as PatternImage } from './assets/pattern.svg';
import { ReactComponent as TimeImage } from './assets/time.svg';
import { ReactComponent as ArrowImage } from './assets/arrow.svg';
import { useMemoizedFn } from 'ahooks';
import { isEqual } from 'lodash';
import './MqttDetail.css';
import { verifyFailImg, verifySuccessImg } from './constant';
import { CloseOutlined } from '@ant-design/icons';

export default function DataTabs(props: any) {
  const [activeKey, setActiveKey] = useState(0);
  const classStr = 'DataTabItem';
  const onTabChange = (e: React.MouseEvent<HTMLDivElement>, key: number) => {
    setActiveKey(key);
    (e.target as HTMLElement)?.scrollIntoView({ behavior: 'smooth', inline: 'center' });
  };

  const tabList = props.items || [];
  return (
    <div className="DataTabListBox">
      {activeKey > 2 && <div className="DataTabListBox-left "></div>}
      {activeKey < tabList.length - 3 && <div className="DataTabListBox-right"></div>}
      <div className="overflow-hidden ">
        <div className="DataTabList">
          {tabList.map((item: any, index: number) => {
            return (
              <div
                key={index}
                className={`${classStr} ${activeKey === index ? 'is-active' : ''}`}
                onClick={(e) => onTabChange(e, index)}
              >
                {item.label}
              </div>
            );
          })}
        </div>
      </div>
      <div className="">{tabList[activeKey]?.children}</div>
    </div>
  );
}

function DeviceBlock(props: any) {
  return (
    <div className="text-txt-main bg-white p-[16px] rounded-[8px] border border-solid border-line-border flex-1 mt-[16px] min-w-0">
      {props.children}
    </div>
  );
}

function DataBlock(props: any) {
  return (
    <div className="DataBlock">
      {props.title || props.icon ? (
        <div className="flex items-center h-[20px]">
          {props.icon}
          <span className="ml-[8px]">{props.title}</span>
        </div>
      ) : null}
      <div className="flex flex-wrap mt-[8px]">{props.children}</div>
    </div>
  );
}

function VerifyProperty(props: any & { className: string }) {
  const { className } = props;
  const { label, result: value, reason } = props.data || {};
  return (
    <div className={`text-[14px] leading-[20px] mt-[12px] mr-[26px] ${className || ''}`}>
      <div className="flex font-medium">
        <div className="w-[26px] flex items-center">
          <div className="size-[4px] bg-txt-main rounded-[4px] ml-[7px]"></div>
        </div>
        <div className="flex-shrink-0 mr-[2px]">{label}</div>
        {value === 1 ? (
          <span className="text-sys-green">通过</span>
        ) : (
          <span className="text-sys-red">存疑</span>
        )}
      </div>
      {value !== 1 ? <div className="text-txt-sub mt-[4px] pl-[26px]">依据：{reason}</div> : null}
    </div>
  );
}
function DeviceSubTabList(props: any) {
  const { conclusion, verDimRes } = props.data || {};
  const { habitatMatch, speciesDistribution, seasonalActvt, historyTrace, bodyFeature } =
    verDimRes || {};
  const { tempVrf, humVrf, windSpeedVrf, co2Vrf, weatherVrf, surroundingVrf } = habitatMatch || {};
  const { regionVrf, prefRegionVrf } = speciesDistribution || {};
  const { survSeasVrf, dayNightPrefVrf } = seasonalActvt || {};
  const { histAreaAppearVrf, histDiseaseVrf } = historyTrace || {};
  const [itemList, setItemList] = useState<any>([]);
  useEffect(() => {
    let list = [
      {
        name: '形态特征验证：',
        icon: <PatternImage className="size-[20px] mr-[6px]" />,
        isOpen: false,
        result: 0,
        properties: [
          { label: '蚊属形态验证：', ...bodyFeature?.genus },
          { label: '蚊种形态验证：', ...bodyFeature?.species },
          { label: '性别验证：', ...bodyFeature?.gender },
        ],
      },
      {
        name: '生境环境验证：',
        icon: <EnvImage className="size-[20px] mr-[6px]" />,
        isOpen: false,
        result: 0,
        properties: [
          { label: '温度验证结果：', ...tempVrf },
          { label: '湿度验证结果：', ...humVrf },
          { label: '风速验证结果：', ...windSpeedVrf },
          { label: '二氧化碳浓度验证结果：', ...co2Vrf },
          { label: '天气验证结果：', ...weatherVrf },
          { label: '生境匹配度验证结果：', ...surroundingVrf },
          { label: '喜好地域验证结果：', ...prefRegionVrf },
          { label: '生存季节验证结果：', ...survSeasVrf },
          { label: '昼夜活跃偏好验证结果：', ...dayNightPrefVrf },
        ],
      },
      {
        name: '历史出现痕迹验证：',
        icon: <TimeImage className="size-[20px] mr-[6px]" />,
        isOpen: false,
        result: 0,
        properties: [
          { label: '历史出现该区域类型蚊种验证：', ...histAreaAppearVrf },
          { label: '历史爆发该蚊种传播的病害验证：', ...histDiseaseVrf },
        ],
      },
    ];
    list = list.map((item) => {
      const result = item.properties.every((rule) => rule.result === 1) ? 1 : 0;
      return {
        ...item,
        result,
      };
    });
    setItemList(list);
  }, []);

  const openItem = (index: number) => {
    itemList[index].isOpen = !itemList[index].isOpen;
    setItemList([...itemList]);
  };
  return (
    <ul className="mt-[20px] overflow-hidden w-full">
      {itemList.map((item: any, index: number) => {
        return (
          <li key={item.name} className={'VerifyProgressItem'}>
            <ArrowImage
              className="VerifyProgressItem-arrow"
              style={{
                transform: `rotate(${item.isOpen ? '0deg' : '180deg'})`,
              }}
              onClick={() => openItem(index)}
            />
            <div className="VerifyProgressItem-result">
              {item.icon}
              <span className="mr-[2px]">{item.name}</span>
              {item.result === 1 ? (
                <span className="flex">
                  <img src={verifySuccessImg} className="mr-[4px] size-[20px]" />
                  通过
                </span>
              ) : (
                <span className="flex">
                  <img src={verifyFailImg} className="mr-[4px] size-[20px]" />
                  部分存疑
                </span>
              )}
            </div>
            {item.isOpen ? (
              <div>
                {item.properties
                  ? item.properties.map((item: any, idx: number) => {
                      return <VerifyProperty data={item} key={idx} />;
                    })
                  : null}
              </div>
            ) : null}
          </li>
        );
      })}
    </ul>
  );
}

function EnvInfo(props: any) {
  const { weather } = props.data || {};
  const { now, temperature, humidity, wind_speed, co2 } = weather || {};
  return (
    <DataBlock
      icon={
        <IconEnvNew
          className="text-btn-blue size-[20px] "
          style={{ '--stoke-color': '#fff' }}
        ></IconEnvNew>
      }
      title={'环境信息'}
    >
      <div className="flex flex-wrap gap-x-[24px] gap-y-[8px] ">
        <div>天气：{now} </div>
        <div>温度：{concatString(temperature, '°C')} </div>
        <div>湿度：{concatString(humidity, '%RH')}</div>
        <div>二氧化碳浓度：{concatString(co2, 'PPM')} </div>
        <div>风速：{concatString(wind_speed, 'm/s')}</div>
      </div>
    </DataBlock>
  );
}

function DeviceProcess(props: any) {
  const agent_info = props.data?.agent_info;
  const items = useMemo(() => {
    const list = Array.isArray(agent_info) ? agent_info : [];
    return list.map((item: any, index: number) => {
      return {
        label:
          item.name || MOS_NAME_LIST_EN[item.type.toLowerCase()] || item.type || `蚊媒${index}`,
        key: String(index), // key 必须为字符串
        children: <DeviceSubTabList data={item} key={index} />,
      };
    });
  }, [agent_info]);

  if (!items?.length) {
    return null;
  }

  return (
    <DataBlock
      icon={<IconProcess className="text-sys-green" style={{ '--stroke-color': '#fff' }} />}
      title="验证过程"
    >
      <DataTabs destroyInactiveTabPane tabBarStyle={{ marginBottom: 0 }} items={items} />
    </DataBlock>
  );
}

type RecResultRef = {
  check: () => Promise<boolean>;
};

export const MqttDetail = (props: any) => {
  const { currentItem, visible, onClose, showPicModal, mosqutioList, queryTableData } = props;
  const [isFormChanged, setIsFormChanged] = useState<boolean>(false);
  const recordRef = useRef<any>([]);
  const [detail, setDetail] = useState<any>({});
  const recRef = useRef<RecResultRef>(null);
  useEffect(() => {
    if (visible) {
      request(MosquitoAPI.getAuditDetail(currentItem.id), {
        method: 'get',
        params: {
          tenant_id: 1,
        },
      }).then((res: any) => {
        if (res.code === 200) {
          setDetail(res.data || {});
        } else {
          message.info(res.message || '查询验证过程失败');
          onClose();
        }
      });
    }
  }, [visible, currentItem]);

  const [drawerVisible, setDrawerVisible] = useState(visible);
  const closeDrawer = () => {
    setDrawerVisible(false);
  };

  const onDrawerOpenChange = (open: boolean) => {
    if (open) {
    } else {
      onClose();
    }
  };
  const onMosChange = useMemoizedFn((allValues: any) => {
    const preValue = detail?.audit_record_list || [];
    const changed = !isEqual(allValues, preValue);
    recordRef.current = allValues;
    setIsFormChanged(changed);
  });

  const postMosVerifyData = useMemoizedFn(() => {
    // if (!isFormChanged) {
    //   return false;
    // }
    recRef.current?.check?.().then((values) => {
      request(MosquitoAPI.postSubmitMosquito, {
        method: 'post',
        data: {
          task_id: currentItem.task_id,
          records: recordRef.current,
        },
      }).then((res) => {
        if (res.code === 200) {
          message.info('验证数据更新成功', 3);
          queryTableData();
          closeDrawer();
        } else {
          message.info(res.message || '保存失败');
        }
      });
    });
  });

  return (
    <>
      <XcDrawer
        title={false}
        closable={false}
        mask={false}
        maskClosable={true}
        open={drawerVisible}
        width={500}
        destroyOnClose={true}
        afterOpenChange={onDrawerOpenChange}
        onClose={closeDrawer}
        editColumnWidth={420}
        rootClassName="MqttDetailDrawer"
      >
        <div className="MqttDetail-head">
          监测详情
          <CloseOutlined className="size-[10px]" onClick={closeDrawer} />
        </div>
        <div className="MqttDetail-device">
          <div>{currentItem?.device_name || '--'}</div>
          <div className="MqttDetail-tag">设备名称</div>
        </div>
        <div className="flex-1 xc-scrollbar-none min-h-0">
          <div className="DataBlock is-data-position">
            <div>数据编号: {currentItem?.id}</div>
            <div>位置: {currentItem?.monitor_location}</div>
            <div>时间: {currentItem?.created_at}</div>
          </div>
          <EnvInfo data={detail}></EnvInfo>
          <DataBlock
            icon={
              <IconImage
                className="text-btn-blue size-[20px] "
                style={{ '--stoke-color': '#fff' }}
              ></IconImage>
            }
            title={'监测图片'}
          >
            <div className="w-full h-[260px] overflow-hidden rounded-[4px]">
              <img
                src={currentItem.image_url + '?imageMogr2/thumbnail/436x260'}
                className="size-full cursor-pointer"
                onClick={() => showPicModal(currentItem.image_url, true)}
              />
            </div>
          </DataBlock>
          <DataBlock>
            <RecognitionResult
              ref={recRef}
              currentItem={currentItem}
              detail={detail}
              mosqutioList={mosqutioList}
              onChange={onMosChange}
              onVerifyFinish={postMosVerifyData}
            ></RecognitionResult>
          </DataBlock>
          <DeviceProcess data={detail} currentItem={currentItem}></DeviceProcess>
        </div>
        <div className="mx-[-30px] mb-0  border-line-split border-b-[1px] border-solid "></div>
        <div className="xc-form-footer ">
          <Space>
            <Button
              type="primary"
              htmlType="submit"
              className={'xc-form-button-submit'}
              disabled={false}
              onClick={postMosVerifyData}
            >
              确定
            </Button>
          </Space>
        </div>
      </XcDrawer>
    </>
  );
};
