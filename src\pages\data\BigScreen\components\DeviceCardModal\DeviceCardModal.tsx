import { BigScreenAPI, deviceAPI } from '@/api';
import deviceImg from '@/assets/device/img_device.png';
import { px2font, px2vh, px2vw } from '@/components/LargeScreen/vw/vw';
import { XcModal } from '@/components/XcModal/XcModal';
import { XcModalForm } from '@/components/XcModal/XcModalForm';
import { DEVICE_STATUS } from '@/constants/common';
import { formatNumberWithCommas } from '@/utils/number.util';
import { isBlank } from '@/utils/string.util';
import { LoadingOutlined } from '@ant-design/icons';
import { ProFormText } from '@ant-design/pro-components';
import { styled, useModel, useRequest } from '@umijs/max';
import { Button, ConfigProvider, message, Spin } from 'antd';
import 'dayjs/plugin/duration';
import dayjs from 'dayjs';
import { omitBy } from 'lodash';
import '@/css/vw/DeviceCardModal.css';
import {
  CSSProperties,
  forwardRef,
  memo,
  useCallback,
  useEffect,
  useImperativeHandle,
  useMemo,
  useState,
} from 'react';
import { SvgCameraOffline } from '@/components/Icon/device/SvgCameraOffline';
import { SvgCameraOnline } from '@/components/Icon/device/SvgCameraOnline';
import { IconClose } from '@/assets/svg';
import { DeviceStatus } from '@/pages/data/components/DeviceStatus/DeviceStatus';
interface IProps {
  onShow?: () => void;
  style?: CSSProperties;
  control?: boolean; //控制开关
  onAfterSwitch?: (device: any) => void;
}

const DeviceCardModal = memo(
  forwardRef((props: IProps, ref) => {
    const { control = true, style, onAfterSwitch } = props;
    const [currentDevice, setCurrentDevice] = useState<Record<string, any>>({});
    const { setModelDevice } = useModel('useDevice', (m) => ({
      setModelDevice: m.setCurrentDevice,
    }));
    const { setOverview } = useModel('useMonitor', (m) => ({
      setOverview: m.setOverview,
    }));
    const { region } = useModel('useMenu', (m) => ({ region: m.region }));
    const { setDeviceModalVisible, deviceModalVisible } = useModel('useDevice');
    const [confirmVisible, setConfirmVisible] = useState(false);

    // 区域数据
    const regionOverViewRequest = useRequest(
      (data?: any, headers?: any) => ({
        url: BigScreenAPI.regionOverview,
        params: omitBy({ ...data, tenant_id: 1 }, isBlank),
        headers,
      }),
      {
        manual: true,
        formatResult: (r) => r,
        initialData: { data: [], code: 200, msg: '' },
      },
    );
    // 设备详情请求接口
    const deviceStatRequest = useRequest(
      (id, data?: any, headers?: any) => ({
        url: BigScreenAPI.deviceStat(id),
        params: omitBy({ ...data, tenant_id: 1 }, isBlank),
        headers,
      }),
      {
        manual: true,
        formatResult: (r) => r,
        initialData: { data: {} },
      },
    );
    /**
     * 更新设备请求接口
     */
    const deviceStatusRequest = useRequest(
      (id, data?: any, headers?: any) => ({
        method: 'PUT',
        url: deviceAPI.deviceStatus(id),
        data: { ...data },
        headers,
      }),
      {
        manual: true,
        formatResult: (r) => r,
        initialData: { result: null },
        throwOnError: true,
      },
    );

    // 切换设备状态
    const onSwitchStatusRequest = async (
      device_id: any,
      { tenant_id, status, offline_reason }: any,
    ) => {
      try {
        const switchResult = await deviceStatusRequest.run(device_id, {
          tenant_id: tenant_id,
          status: status,
          offline_reason: offline_reason,
        });

        // 刷新下设备统计
        const [regionResult, detailResult] = await Promise.all([
          regionOverViewRequest.run({ ...region, device_id: device_id }),
          deviceStatRequest.run(device_id),
        ]);
        if (regionResult.code === 200) {
          setOverview(regionResult.data);
        }

        if (detailResult.code === 200) {
          const { status } = detailResult.data;
          if (onAfterSwitch) {
            onAfterSwitch({ id: device_id, status });
          }
        }
        if (switchResult.code === 200) {
          message.success(`${status === 1 ? '启动监测' : '停止监测'}成功`);
          return true;
        }
        message.error(switchResult.msg ?? switchResult.message);
        return false;
      } catch (error: any) {
        message.error(error.info.msg ?? error.info.message);
        return false;
      }
    };
    const onClickDevice = useCallback((device: any) => {
      if (device && device.id) {
        setDeviceModalVisible(true);
        setCurrentDevice(device);
        setModelDevice(device);
        deviceStatRequest.run(device.id);
      }
    }, []);
    const onOffline = useCallback(() => {
      setConfirmVisible(true);
    }, []);
    const onOnline = useCallback(async () => {
      await onSwitchStatusRequest(currentDevice.id, {
        tenant_id: currentDevice.tenant_id,
        status: 1,
        offline_reason: '启动监测',
      });
    }, [currentDevice]);

    const device_info = useMemo(() => {
      const { area_type, created_at } = currentDevice;
      return {
        area_type: area_type ?? '',
        created_at: created_at,
        first_online_time: deviceStatRequest?.data?.data?.first_online_time,
        total_duration: deviceStatRequest?.data?.data?.total_duration,
        device_name: deviceStatRequest?.data?.data?.device_name,
        device_code: deviceStatRequest?.data?.data?.device_code,
        total_count: deviceStatRequest?.data?.data?.total_count,
        online_hours: (deviceStatRequest?.data?.data?.total_duration / 3600).toFixed(1),
        status: deviceStatRequest?.data?.data?.status,
        top_species: deviceStatRequest?.data?.data?.type_stats ?? [],
      };
    }, [currentDevice, deviceStatRequest.data]);

    useImperativeHandle(ref, (): any => {
      return {
        onOpen: onClickDevice,
      };
    });
    useEffect(() => {
      return () => {
        setDeviceModalVisible(false);
      };
    }, []);
    return (
      <ConfigProvider
        theme={{
          components: {
            Button: {
              defaultHoverBorderColor: '#ffffff',
            },
          },
        }}
      >
        <XcModal
          wrapClassName="passevent"
          open={deviceModalVisible}
          style={style}
          styles={{
            wrapper: {
              boxShadow: '0px 8px 20px rgba(0, 0, 0, 0.40)',
            },
            content: {
              padding: 0,
              margin: 0,
              borderRadius: 'none',
            },
          }}
          getContainer={false}
          onOk={() => {
            setDeviceModalVisible(false);
          }}
          onCancel={() => {
            setDeviceModalVisible(false);
          }}
          maskClosable={false}
          mask={false}
        >
          <div className={`DeviceCardModal ${control ? 'control' : ''}`}>
            {deviceStatRequest.loading ? (
              <div className="w-full h-full flex justify-center items-center">
                <Spin indicator={<LoadingOutlined spin />} />
              </div>
            ) : (
              <>
                <div className="title ">
                  <div className="title-left">
                    {device_info.status === 1 ? <SvgCameraOnline /> : <SvgCameraOffline />}
                    <p>{device_info.device_name}</p>
                  </div>
                  <IconClose
                    className="title-close"
                    onClick={() => {
                      setDeviceModalVisible(false);
                    }}
                  />
                </div>
                <div className="content">
                  <div className="left">
                    <img src={deviceImg} className="left-img" />
                    {control && (
                      <Button
                        ghost
                        type="primary"
                        className="left-btn"
                        onClick={device_info.status === 1 ? onOffline : onOnline}
                      >
                        {device_info.status === 1 ? '停止监测' : '启动监测'}
                      </Button>
                    )}
                  </div>
                  <div className="right">
                    {control && (
                      <div>
                        <span>
                          监测总数：
                          <span>{formatNumberWithCommas(device_info.total_count)}</span>
                        </span>
                      </div>
                    )}

                    <div>
                      <span>
                        设备编号：
                        <span>
                          {isBlank(device_info.device_code) ? '-' : device_info.device_code}
                        </span>
                      </span>
                    </div>
                    <div>
                      <span className="flex items-center">
                        监测状态：
                        <DeviceStatus
                          status={device_info.status === 1 ? 'success' : 'default'}
                          text={DEVICE_STATUS[device_info.status]}
                        />
                      </span>
                    </div>
                    <div>
                      <span>
                        起始监测时间：
                        <span>
                          {device_info.total_duration ? device_info.first_online_time : '-'}
                        </span>
                      </span>
                    </div>
                    <div>
                      <span>
                        监测时长：
                        <span>
                          {device_info.total_duration
                            ? dayjs
                                .duration(device_info.total_duration, 'seconds')
                                .format('HH:mm:ss')
                            : '-'}
                        </span>
                      </span>
                    </div>
                  </div>
                </div>
              </>
            )}
          </div>
        </XcModal>
        <XcModalForm
          title="确定停止监测？"
          autoFocusFirstInput
          width={500}
          open={confirmVisible}
          onOpenChange={setConfirmVisible}
          modalProps={{
            zIndex: 1000,
            destroyOnClose: true,
          }}
          onFinish={async (values) => {
            const { reason } = values;
            const result = await onSwitchStatusRequest(currentDevice.id, {
              tenant_id: currentDevice.tenant_id,
              status: 2,
              offline_reason: reason,
            });
            return result;
          }}
        >
          <div className="my-[40px]">
            <ProFormText
              name={'reason'}
              required={false}
              rules={[{ required: true, message: '请填写停止监测原因...' }]}
              placeholder={'请填写停止监测原因...'}
            />
          </div>
        </XcModalForm>
      </ConfigProvider>
    );
  }),
);

export default DeviceCardModal;
