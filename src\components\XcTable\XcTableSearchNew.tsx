import { ConfigProvider, Form, Input } from 'antd';
import { forwardRef, memo, useEffect, useImperativeHandle, useMemo, useRef } from 'react';
import { IconClose } from '../Icon/IconClose';
import { IconDrop } from '../Icon/IconDrop';
import { IconSearch } from '../Icon/IconSearch';
import { XcInput } from '../XcInput';
import { XcRangePicker } from '../XcRangePicker';
import { XcSelect } from '../XcSelect';
import { IColumns } from './interface/search.interface';
import { ProForm } from '@ant-design/pro-components';
import { styled } from '@umijs/max';

interface IProps {
  columns?: IColumns[];
  children?: React.ReactNode;
  requestTable: (criteria?: any) => void;
  className?: string;
}
const StyledFormItem = styled(Form.Item)`
  margin-bottom: 20px !important;
  margin-inline-end: 10px !important;
`;
export const XcTableSearchNew = memo(
  forwardRef((props: IProps, ref: any) => {
    const { columns, children, className, requestTable } = props;
    const formRef = useRef<any>(null);
    const disabledDate = (current: any, info: any, rangeLimit: any) => {
      const { from } = info;
      if (rangeLimit) {
        const [time, unit] = rangeLimit;

        return Math.abs(current.diff(from, unit)) >= time;
      }
      return false;
    };

    useImperativeHandle(ref, () => {
      return {
        getFieldsValue: () => {
          return formRef.current?.getFieldsValue();
        },
      };
    });

    useEffect(() => {
      const data = columns?.reduce((acc, cur) => {
        const { key, initialValue } = cur;
        if (key && initialValue) {
          acc[key] = initialValue;
        }
        return acc;
      }, {} as any);
      formRef.current.setFieldsValue(data);
    }, []);

    return (
      <ConfigProvider
        theme={{
          components: {
            Input: {
              colorBgContainer: '#F4F4F6',
              colorBorder: '#F4F4F6',
              activeBorderColor: '#E7E9ED',
              hoverBorderColor: '#E7E9ED',
            },
          },
        }}
      >
        <div className={'flex justify-between pt-[20px] ' + className}>
          <ProForm
            layout="inline"
            submitter={false}
            onValuesChange={requestTable}
            formRef={formRef}
            className="items-center flex-wrap"
          >
            {columns?.map((item, index) => {
              const {
                valueType,
                valueEnum = {} as any,
                maxValue,
                minValue,
                rangeLimit,
                placeholder,
                fieldProps,
                width,
                height,
                render,
                key,
              } = item;
              if (render) {
                return (
                  <StyledFormItem key={key} name={key}>
                    {render('', item, index, {
                      onChange: (data: any) => {
                        requestTable();
                      },
                    })}
                  </StyledFormItem>
                );
              } else if (valueType === 'drop') {
                let options = [];
                if (Array.isArray(valueEnum)) {
                  options = valueEnum.map((item) => {
                    return {
                      label: item.name,
                      value: item.id,
                      ...item,
                    };
                  });
                } else {
                  options = Object.keys(valueEnum).map((key) => {
                    return { label: valueEnum[key], value: key };
                  });
                }

                return (
                  <StyledFormItem name={key} key={key}>
                    <XcSelect
                      style={{ width }}
                      onSelect={(e) => {
                        requestTable();
                      }}
                      options={options}
                      placeholder={placeholder}
                    />
                  </StyledFormItem>
                );
              } else if (valueType === 'rangePicker' && key) {
                return (
                  <StyledFormItem key={key} name={key}>
                    <XcRangePicker
                      maxDate={maxValue}
                      minDate={minValue}
                      style={width ? { width } : undefined}
                      disabledDate={(current, info) => disabledDate(current, info, rangeLimit)}
                      suffixIcon={<IconDrop className="size-[24px]" />}
                      onChange={(e) => {
                        requestTable();
                      }}
                      allowClear={{
                        clearIcon: (
                          <IconClose
                            onClick={() => {
                              setTimeout(() => {
                                requestTable();
                              }, 0);
                            }}
                          />
                        ),
                      }}
                      {...fieldProps}
                    />
                  </StyledFormItem>
                );
              } else {
                if (key) {
                  return (
                    <StyledFormItem key={key} name={key}>
                      <XcInput
                        allowClear={{
                          clearIcon: (
                            <IconClose
                              onClick={() => {
                                setTimeout(() => {
                                  requestTable();
                                }, 0);
                              }}
                            />
                          ),
                        }}
                        style={{
                          width: width || '320px',
                          height: height || '40px',
                          borderRadius: '8px',
                        }}
                        placeholder={placeholder}
                        prefix={
                          <IconSearch className="ml-[16px] mr-[8px] size-[20px] text-txt-sub " />
                        }
                        onPressEnter={requestTable}
                        onBlur={requestTable}
                        onChange={requestTable}
                      />
                    </StyledFormItem>
                  );
                }
                return null;
              }
            })}
          </ProForm>
          <div className="flex items-end mb-[20px]">{children}</div>
        </div>
      </ConfigProvider>
    );
  }),
);
