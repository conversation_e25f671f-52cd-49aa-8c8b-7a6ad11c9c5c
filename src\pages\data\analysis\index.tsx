import { Tabs } from 'antd';
import React, { useEffect, useMemo } from 'react';
import { request, useLocation, useModel, useNavigate } from '@umijs/max';
import { checkFunPermission, STATIC_PERMISSION_CODE } from '@/utils/permission';
import StyledTabs from '@/components/XcAntdTabs';
import Region from './region';
import Density from './density';
import Growth from './growth/Growth';
import Habitat from './habitat';
import { deviceAPI } from '@/api';
import { filterTreeWidthProperty } from '@/utils/tree.util';

export default function EnterpriseManage() {
  const { menu } = useModel('useMenu', (m) => ({
    menu: m.menu,
    defaultOpenKeys: m.defaultOpenKeys,
  }));
  const { setRegionDeviceList } = useModel('useDevice', (m) => ({
    setRegionDeviceList: m.setRegionDeviceList,
  }));
  const location = useLocation();
  const navigate = useNavigate();
  const pathname = location?.pathname.split('/').pop();

  useEffect(() => {
    request(deviceAPI.getAuthDeviceTree, {
      method: 'GET',
      params: {
        tenant_id: 1,
        page: 1,
      },
    }).then((res: any) => {
      if (res && res.code === 200) {
        const list = res.data?.list || [];
        setRegionDeviceList(list);
      }
    });
  }, []);
  // 过滤调menu树中含有id的项
  const newMenu = filterTreeWidthProperty(menu, 'id');

  const tabs = useMemo(
    () =>
      [
        {
          key: 'region',
          label: '蚊虫高发区域分析',
          code: STATIC_PERMISSION_CODE.地域与构成分布,
          children: <Region menu={newMenu} />,
        },
        {
          key: 'density',
          label: '蚊虫密度与数量分析',
          code: STATIC_PERMISSION_CODE.密度与活跃分布,
          children: <Density menu={menu} />,
        },
        {
          key: 'growth',
          label: '蚊虫增长率分析',
          code: STATIC_PERMISSION_CODE.增长率,
          children: <Growth menu={menu} />,
        },
        {
          key: 'habitat',
          label: '蚊虫高发生境分析',
          code: STATIC_PERMISSION_CODE.生境分布,
          children: <Habitat menu={newMenu} />,
        },
      ].filter((item) => {
        if (item.code) {
          return checkFunPermission(item.code as string);
        }
        return true;
      }),
    [newMenu, menu],
  );
  const activeKey = useMemo(() => {
    return tabs.map((item) => item.key).includes(pathname || '') ? pathname : tabs?.[0]?.key;
  }, [pathname]);

  useEffect(() => {
    if (!activeKey) {
      navigate('/welcome');
    }
  }, [activeKey]);
  const onChange = (key: string) => {
    navigate(`/data/analysis/${key}`);
  };

  if (!activeKey) {
    return null;
  }

  return (
    <div className="flex flex-1 flex-col mt-[20px] mb-[16px] min-h-0">
      {/* <StyledTabs
        defaultActiveKey={activeKey}
        activeKey={activeKey}
        onChange={onChange}
        className="text-txt-main h-[40px]"
        destroyInactiveTabPane
        items={tabs}
        tabBarStyle={{ marginBottom: 0 }}
      ></StyledTabs> */}
      {tabs?.find((item) => item.key === activeKey)?.children}
    </div>
  );
}
