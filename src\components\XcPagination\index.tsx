import { styled } from '@umijs/max';
import { Pagination, PaginationProps } from 'antd';

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-29 18:47:13
 * @LastEditTime: 2025-02-07 16:02:07
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
const StyledPagination = styled(Pagination)`
  display: flex;
  .ant-select-selector,
  .ant-pagination-prev,
  .ant-pagination-next,
  .ant-pagination-item {
    border: 1px solid var(--line-split);
    border-radius: 4px;
    color: var(--txt-main);
    box-sizing: border-box;
  }
  .ant-pagination-item-active {
    > a {
      color: var(--txt-main) !important;
    }
    background-color: color-mix(in srgb, var(--txt-third) 15%, transparent);
  }
  .ant-pagination-options {
    height: 30px;
  }
  .ant-select-item-option-content {
    color: var(--txt-main);
  }
  .ant-pagination-total-text {
    flex: 1;
  }
`;
export const XcPagination = StyledPagination;
export default StyledPagination;
