import EChartsCommon from '@/components/Charts/Charts/EChartsCommon';
import '@/css/vw/DailyActivityLineChart.css';
import { ReactComponent as DailyIcon } from '../assets/day-icon.svg';
import { useModel, useRequest } from '@umijs/max';
import { statisticsAPI } from '@/api';
import dayjs from 'dayjs';
import { useEffect, useMemo, useRef } from 'react';
import { EChartsOption } from 'echarts';
import { getRangeByTimeUnitAndFormat, TIME_UNITS } from '@/utils/time.util';
import { DATE_FMT } from '@/utils/time.util';
import { getMosColor, getMosName } from '@/utils/mosqutio.util';
import { Spin } from 'antd';

export default function DailyActivityLineChart(props: any) {
  const { region, startTime, endTime, monitorType } = props;
  const { mosquitoList } = useModel('useMosquito', (m) => {
    return {
      mosquitoList: m.mosquitoList,
    };
  });
  const queryRef = useRef<any>({});
  const {
    run: getDailyActivity,
    data: dailyActivityRes,
    loading,
  } = useRequest(
    (data?: any, headers?: any) => {
      const mosNameList = mosquitoList.map((item: any) => item.code);
      queryRef.current = { ...(data || {}), monitor_type: mosNameList.join(',') };
      return {
        url: statisticsAPI.getActivityTrend,
        method: 'get',
        params: {
          tenant_id: 1,
          ...queryRef.current,
        },
        headers,
      };
    },
    {
      manual: true,
    },
  );

  useEffect(() => {
    if ((!region?.province && !region?.device_id) || !mosquitoList?.length) {
      return;
    }
    const [startTime1, endTime1] = getRangeByTimeUnitAndFormat(TIME_UNITS.WEEK, DATE_FMT.DATE_TIME);
    const { device_id, ...rest } = region;
    getDailyActivity({
      ...rest,
      dev_ids: device_id,
      start_time: startTime || startTime1,
      end_time: endTime || endTime1,
      time_unit: 'day',
      monitor_type: monitorType,
    });
  }, [region, mosquitoList, monitorType]);

  const dailyActivityData = useMemo(() => {
    const list = dailyActivityRes?.type_groups || [];
    let result = list.slice(0);
    return result;
  }, [dailyActivityRes]);

  const option = useMemo((): EChartsOption => {
    let result = dailyActivityData || [];
    result = result.map((item: any) => {
      return {
        ...item,
        color: getMosColor(item.monitor_type, 'big-screen'),
        name: item.monitor_type === 'all' ? '总蚊虫数量' : getMosName(item.monitor_type) || '其他',
      };
    });
    let xLabels = [];
    if (result?.[0]?.trend_points) {
      xLabels = result?.[0]?.trend_points.map((item: any) => {
        if (queryRef.current.time_unit === 'hour') {
          return dayjs(item.time_point).format(DATE_FMT.TIME);
        } else {
          return item.time_point;
        }
      });
    }
    return {
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        show: true,
        icon: 'roundRect',
        data: result.map((item: any) => item.name),
        textStyle: {
          color: '#fff',
        },
        left: 'center',
        bottom: 0,
      },
      grid: [
        {
          left: 30,
          right: 30,
          top: 20,
          bottom: 40,
          containLabel: true,
        },
      ],
      xAxis: [
        {
          type: 'category',
          data: xLabels,
          axisLabel: {
            rotate: 45,
          },
        },
      ],
      yAxis: [
        {
          type: 'value' as const,
        },
      ],
      series: result.map((item: any) => {
        return {
          name: item.name,
          type: 'line',
          areaStyle: {
            color: item.color,
          },
          itemStyle: {
            color: item.color,
          },
          symbolSize: 6,
          symbol: 'emptyCircle',
          data: item?.trend_points?.map((s: any) => s.activity + 0),
        };
      }),
    };
  }, [dailyActivityRes]);

  return (
    <div className="dalc-card-container">
      <div className="dalc-title-container">
        <DailyIcon className="dalc-title-icon" />
        <h3 className="dalc-title-text">日活动节律</h3>
      </div>
      <div className="dalc-chart-legend-container">
        <div className="dalc-chart-wrapper">
          {dailyActivityData?.length > 0 ? (
            <EChartsCommon option={option} />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-txt-sub">
              {loading ? <Spin /> : '暂无数据'}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
