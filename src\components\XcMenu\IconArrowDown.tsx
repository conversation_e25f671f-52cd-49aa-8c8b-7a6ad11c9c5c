/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-02-09 02:33:26
 * @LastEditTime: 2025-02-11 11:30:31
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
export const IconArrowDown = (props: any) => {
  const { className, ...rest } = props;

  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      {...rest}
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g opacity="0.5">
        <path
          d="M4.66321 5H11.3324C11.7015 5 12 5.27353 12 5.61438C12 5.77283 11.9341 5.91616 11.8284 6.02474L8.53033 10.0888C8.30933 10.358 7.89322 10.4145 7.5994 10.2104C7.54769 10.1757 7.50301 10.1344 7.46775 10.0888L4.1319 5.98135C3.91334 5.71214 3.9721 5.32579 4.2659 5.12383C4.38584 5.03913 4.52454 5.00006 4.66321 5.00006V5Z"
          fill="white"
        />
      </g>
    </svg>
  );
};
