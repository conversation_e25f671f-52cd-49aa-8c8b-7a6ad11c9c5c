import { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, Col, message, Row, Space, Tree } from 'antd';
import { useRequest } from '@umijs/max';
import {
  ProForm,
  ProFormDateRangePicker,
  ProFormInstance,
  ProFormItem,
  ProFormSelect,
  ProFormText,
} from '@ant-design/pro-components';
import { NAME_REG_EXP } from '@/constants/regex';
import { compareArrayIdList } from '@/utils/common.util';
import XcAntdForm from '@/components/XcAntdForm';
import { XcDrawer } from '@/components/XcDrawer';
import { IconDrop } from '@/components/Icon/IconDrop';
import { deviceAPI } from '@/api/device';
import dayjs from 'dayjs';
import { DATE_FMT } from '@/utils/time.util';
import DeviceAreaTree from './DeviceAreaTree';
import DeviceMap from './DeviceMap';

export default function AddOrEdit(props: any) {
  const { modalType, currentItem, queryTableData, visible, onClose } = props;
  const [isFormChanged, setIsFormChanged] = useState(false);
  const [deviceIds, setDeviceIds] = useState<any>([]);
  const [drawerVisible, setDrawerVisible] = useState(visible);
  const [drawerShow, setDrawerShow] = useState(false);
  const [area, setArea] = useState('');
  const formRef = useRef<ProFormInstance>(null);
  const modalNamePrefix = modalType === 1 ? '新增' : '修改';

  useEffect(() => {
    if (modalType === 1) {
      setIsFormChanged(true);
    } else {
      setIsFormChanged(false);
    }
  }, [currentItem, modalType]);

  useEffect(() => {
    if (modalType !== 1) {
      const ids = (currentItem.deviceIds || '').split(',').map((id: string) => +id);
      setDeviceIds([...ids]);
      currentItem.range_time = [dayjs(currentItem.start_time), dayjs(currentItem.end_time)];
      formRef.current?.setFieldsValue(currentItem);
    }
  }, [currentItem]);

  const onDrawerOpenChange = (open: boolean) => {
    if (open) {
      if (modalType !== 1) {
        formRef.current?.setFieldsValue(currentItem);
      } else {
        formRef.current?.setFieldsValue({
          range_time: [dayjs().add(-1, 'day'), dayjs()],
        });
      }
      setDrawerShow(true);
    } else {
      setDrawerShow(false);
      onClose();
    }
  };
  const closeDrawer = () => {
    setDrawerVisible(false);
  };
  /**
   * 新增请求接口
   */
  const deviceGroupAddRequest = useRequest(
    (data?: any, headers?: any) => ({
      method: 'POST',
      url: deviceAPI.postDeviceGroup,
      data: { tenant_id: 1, ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: null },
      throwOnError: true,
    },
  );
  /**
   * 编辑请求接口
   */
  const deviceGroupEditRequest = useRequest(
    (id, data?: any, headers?: any) => ({
      method: 'PUT',
      url: deviceAPI.deviceGroupById(id),
      data: { ...currentItem, ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: null },
      throwOnError: true,
    },
  );

  // 提交编辑表单
  const onFinishEdit = async (item: any) => {
    try {
      const result = await deviceGroupEditRequest.run(currentItem.id, {
        id: currentItem.id,
        ...item,
      });
      if (result.code === 200) {
        message.success(result.message || '修改成功。 ');
        await queryTableData();
        closeDrawer();
        return true;
      } else {
        message.error(result.message || '修改失败，请重试。');
      }
    } catch (error) {
      console.error('🚀 ~ onEditFinish ~ error:', error);
    }

    return false;
  };
  const onFinishAdd = async (item: any) => {
    try {
      const result = await deviceGroupAddRequest.run({
        ...item,
      });

      if (result.code === 200) {
        message.success('新增监测网成功。');
        await queryTableData();
        closeDrawer();
        return true;
      } else {
        message.error(result.message || '新增失败，请重试。');
      }
    } catch (error) {
      console.error('🚀 ~ onAddFinish ~ error:', error);
    }

    return false;
  };
  const onSubmit = () => {
    const values = formRef.current?.getFieldsValue();

    if (values.range_time) {
      const range_time = values.range_time;
      delete values.range_time;
      values.start_time = dayjs(range_time[0]).format(DATE_FMT.DATE_TIME);
      values.end_time = dayjs(range_time[1]).format(DATE_FMT.DATE_TIME);
    }
    if (modalType === 1) {
      return onFinishAdd(values);
    } else {
      return onFinishEdit(values);
    }
  };

  const onValuesChange = (changedValues: any, allValues: any) => {
    if (modalType === 2) {
      const keys = Object.keys(allValues);
      const changed = keys.some((key) => {
        const newValue = allValues[key];
        const preValue = currentItem[key];
        let result = newValue !== preValue;
        if (Array.isArray(newValue) || Array.isArray(preValue)) {
          result = compareArrayIdList(newValue, preValue);
        }
        if (result) {
          // console.log(key, allValues[key], currentItem[key]);
        }
        return result;
      });
      setIsFormChanged(changed);
    }
  };

  const onAreaChange = (area: any) => {
    const { province, city, district, street } = area.split(',');
    formRef.current?.setFieldsValue({
      province,
      city,
      district,
      street,
    });
    const values = formRef.current?.getFieldsValue();
    setArea(area);
    onValuesChange(null, values);
  };

  const onDeviceChange = (deviceIds: any) => {
    formRef.current?.setFieldsValue({
      device_ids: deviceIds,
    });
    console.log('deviceIds ', deviceIds);
    const values = formRef.current?.getFieldsValue();
    onValuesChange(null, values);
  };

  return (
    <>
      {/* <XcTableMask
        visible={drawerVisible}
        setVisible={closeDrawer}
        width={160}
        isFormChanged={isFormChanged}
      ></XcTableMask> */}
      <XcDrawer
        title={false}
        closable={false}
        mask={false}
        maskClosable={false}
        open={drawerVisible}
        width={'70vw'}
        destroyOnClose={false}
        afterOpenChange={onDrawerOpenChange}
        onClose={closeDrawer}
        isFormChanged={isFormChanged}
      >
        <div className="h-[70px] flex items-center px-[30px]">
          <div className="text-[20px] font-medium text-txt-main flex-1">{`${modalNamePrefix}监测网`}</div>
          <Button
            type="text"
            className="text-[24px] text-txt-sub cursor-pointer"
            onClick={closeDrawer}
          >
            &times;
          </Button>
        </div>
        <div className="mt-0  border-line-split border-b-[1px] border-solid "></div>
        <div className="flex-1 xc-scrollbar-none p-[30px]">
          <XcAntdForm
            formRef={formRef}
            autoFocusFirstInput
            onFinish={onSubmit}
            onValuesChange={onValuesChange}
            submitter={{
              render: (_, dom) => null,
            }}
          >
            <div className="flex gap-[20px]">
              <div className="w-[50%]">
                <ProFormText
                  name={'name'}
                  label={'监测网名称'}
                  layout="horizontal"
                  required={true}
                  rules={[
                    {
                      required: true,
                      message: '请填写监测网名称',
                    },
                    {
                      pattern: NAME_REG_EXP,
                      message: '监测网名称格式不正确',
                    },
                  ]}
                  placeholder={'请填写监测网名称'}
                />
              </div>
              <div className="w-[50%]">
                <ProFormSelect
                  name={'status'}
                  label={'是否使用'}
                  layout="horizontal"
                  fieldProps={{
                    className: 'w-full h-[40px]',
                  }}
                  options={[
                    { label: '使用', value: 1 },
                    { label: '停用', value: 0 },
                  ]}
                  required={false}
                  rules={[
                    {
                      required: true,
                      message: '请选择是否使用',
                    },
                  ]}
                  placeholder={'请选择是否使用'}
                />
              </div>
            </div>
            <div className="flex gap-[20px]">
              <div className="w-[50%]">
                <ProFormText
                  name="point_manager"
                  label={'负责人'}
                  fieldProps={{
                    className: 'w-full h-[40px]',
                    maxLength: 20,
                  }}
                  placeholder={'请填写负责人'}
                />
              </div>
              <div className="w-[50%]">
                <ProFormDateRangePicker
                  label="监测时间"
                  required
                  name="range_time"
                  fieldProps={{
                    suffixIcon: <IconDrop className="size-[24px]" />,
                    className: 'w-full h-[40px]',
                  }}
                />
              </div>
            </div>
            <ProFormText hidden name="device_ids"></ProFormText>
            <ProFormText hidden name="province"></ProFormText>
            <ProFormText hidden name="city"></ProFormText>
            <ProFormText hidden name="district"></ProFormText>
            <ProFormText hidden name="street"></ProFormText>
            <ProFormItem label="设备范围" required name="area">
              <DeviceAreaTree
                defaultCheckedKeys={deviceIds}
                onChange={onAreaChange}
                visible={drawerShow}
                deviceType="self"
              />
            </ProFormItem>
            <DeviceMap onChange={onDeviceChange} address={area} />
          </XcAntdForm>
        </div>
        <div className="mb-0  border-line-split border-b-[1px] border-solid px-[30px]"></div>
        <div className="xc-form-footer">
          <Space>
            <Button className="xc-form-button-cancel" onClick={closeDrawer}>
              取消
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              className={`xc-form-button-submit  ${isFormChanged ? '' : 'opacity-30 cursor-default'}`}
              onClick={() => {
                formRef.current?.submit();
              }}
            >
              保存
            </Button>
          </Space>
        </div>
      </XcDrawer>
    </>
  );
}
