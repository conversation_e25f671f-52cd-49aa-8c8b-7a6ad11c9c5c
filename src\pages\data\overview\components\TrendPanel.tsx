import React, { useEffect, useMemo } from 'react';
import Panel from '@/components/Panel';
import EChartsCommon from '@/components/Charts/Charts/EChartsCommon';
import { useRequest, history } from '@umijs/max';
import { statisticsAPI } from '@/api';
import { getMosColor, getMosName } from '@/utils/mosqutio.util';
import { Spin } from 'antd';

const TrendPanel: React.FC<{
  startTime: string;
  endTime: string;
  range: any;
  mosquitoType: string;
}> = (props) => {
  const { startTime, endTime, range, mosquitoType } = props;
  const {
    run: getDensityTrend,
    data: densityTrendRes,
    loading,
  } = useRequest(
    (data?: any, headers?: any) => ({
      url: statisticsAPI.getDensityTrend,
      method: 'GET',
      params: { ...data, tenant_id: 1 },
      headers,
    }),
    {
      manual: true,
      throwOnError: true,
    },
  );

  useEffect(() => {
    if (typeof range?.province === 'undefined') return;
    getDensityTrend({
      time_unit: 'day',
      start_time: startTime,
      end_time: endTime,
      ...range,
      monitor_type: mosquitoType,
    });
  }, [range, mosquitoType]);

  const option = useMemo(() => {
    let result = densityTrendRes?.type_groups?.[0]?.trend_points || [];
    result = result.map((item: any) => {
      return {
        ...item,
        name: item.monitor_type === 'all' ? '总蚊虫密度' : getMosName(item.monitor_type),
        color: getMosColor(item.monitor_type),
      };
    });
    return {
      tooltip: { trigger: 'axis' },
      legend: {
        data: ['总蚊虫密度'],
        show: true,
        top: 10,
        right: 20,
      },
      grid: { left: 40, right: 40, top: 40, bottom: 60 },
      xAxis: [
        {
          type: 'category',
          name: '时间',
          nameLocation: 'end',
          nameTextStyle: { color: '#797A85' },
          data: result.map((item: any) => item.time_point),
          axisLine: { lineStyle: { color: '#E4E6EB' } },
          axisLabel: { color: '#797A85', rotate: 45 },
        },
      ],
      yAxis: [
        {
          type: 'value',
          name: '蚊虫密度：只/有效监测时长',
          nameLocation: 'end',
          nameTextStyle: { color: '#797A85', align: 'left' },
          splitLine: { lineStyle: { color: '#E4E6EB', type: 'dashed' } },
        },
      ],
      series: [
        {
          name: '总蚊虫密度',
          type: 'line',
          symbol: 'circle',
          itemStyle: { color: getMosColor('其他') },
          data: result.map((s: any) => s.density + 0),
          emphasis: { focus: 'series' },
          label: {
            show: true,
            position: 'center',
          },
        },
      ],
    };
  }, [densityTrendRes]);

  const gotoAnalysis = () => {
    history.push('/data/analysis/density');
  };
  return (
    <Panel
      title={
        <span className="flex items-center gap-2">
          <span>蚊虫密度消长趋势</span>
          <div className="text-[12px] text-[#999]">
            （该模块展示的是总蚊虫密度随时间的变化趋势）
          </div>
        </span>
      }
      actions={
        <div onClick={gotoAnalysis} className=" cursor-pointer">
          <svg width="20" height="20" fill="none">
            <circle cx="4" cy="10" r="2" fill="#797A85" />
            <circle cx="10" cy="10" r="2" fill="#797A85" />
            <circle cx="16" cy="10" r="2" fill="#797A85" />
          </svg>
        </div>
      }
    >
      <div className="w-full h-[260px] flex items-center justify-center">
        {loading ? <Spin /> : <EChartsCommon option={option} />}
      </div>
    </Panel>
  );
};

export default TrendPanel;
