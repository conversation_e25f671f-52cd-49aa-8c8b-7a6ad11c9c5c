import { deviceAPI } from '@/api/device';
import { SvgSearch } from '@/components/Icon/SvgSearch';
import JsonEditor from '@/components/JsonEditor/JsonEditor';
import { IColumns } from '@/components/XcTable/interface/search.interface';
import { ITableRef, XcTableNew } from '@/components/XcTable/XcTableNew';
import { DATE_FMT } from '@/utils/time.util';
import { useRequest } from '@umijs/max';
import { useMemoizedFn } from 'ahooks';
import { Button, message, Typography } from 'antd';
import dayjs from 'dayjs';
import { useRef, useState } from 'react';

const { Text } = Typography;

export default () => {
  const tableRef = useRef<ITableRef>(null);

  // 请求接口
  const queryMQTTLogRequest = useRequest(
    (data?: any, headers?: any) => ({
      url: deviceAPI.deviceMQTTLog,
      params: { ...data, tenant_id: 1 },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: { total: 0, page: 1, pageSize: 20, list: [] } },
      throwOnError: true,
    },
  );
  const [editorData, setEditorData] = useState<Record<string, any> | null>(null);

  const closeEditorModal = () => {
    setEditorData(null);
  };
  const showJsonModal = useMemoizedFn((data: Record<string, any>) => {
    setEditorData(data);
  });
  const columns: IColumns[] = [
    {
      title: '入库时间',
      key: 'range_time',
      realtime: true,
      hideInTable: true,
      initialValue: [dayjs().subtract(30, 'day'), dayjs()],
      maxValue: dayjs(),
      minValue: dayjs('2024-12-01'),
      rangeLimit: [18, 'month'],
      valueType: 'rangePicker',
    },
    {
      title: '设备编号',
      width: 320,
      key: 'code',
      hideInTable: true,
      placeholder: '搜索设备编号',
    },
    {
      title: '设备ID',
      dataIndex: 'device_id',
      key: 'device_id',
      hideInSearch: true,
      render: (_, item: { device_id: string }) => {
        return <Text ellipsis={{ tooltip: item.device_id }}>{item.device_id}</Text>;
      },
    },
    {
      title: '主题',
      dataIndex: 'topic',
      key: 'topic',
      hideInSearch: true,
      render: (_, item: { topic: string }) => {
        return <Text ellipsis={{ tooltip: item.topic }}>{item.topic}</Text>;
      },
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 80,
      hideInSearch: true,
    },
    {
      title: '子类型',
      dataIndex: 'sub_type',
      key: 'sub_type',
      width: 80,
      hideInSearch: true,
    },
    {
      title: '请求数据',
      dataIndex: 'req_data',
      key: 'req_data',
      render: (value: string, record: any) => {
        return (
          <div
            className="text-one-row cursor-pointer"
            onClick={() =>
              showJsonModal({
                json: value,
                title: `日志编号-${record.id} 请求数据：`,
              })
            }
          >
            {value}
          </div>
        );
      },
    },
    {
      title: '响应数据',
      dataIndex: 'res_data',
      key: 'res_data',
      render: (value: string, record: any) => {
        return (
          <div
            className="text-one-row cursor-pointer"
            onClick={() =>
              showJsonModal({
                json: value,
                title: `日志编号-${record.id} 响应数据：`,
              })
            }
          >
            {value}
          </div>
        );
      },
    },
    {
      title: '上下行',
      dataIndex: 'direction',
      key: 'direction',
      hideInSearch: true,
      render: (_, item: { direction: string }) => {
        return item.direction === 'up' ? '上行' : '下行';
      },
    },
    {
      title: '消息ID',
      dataIndex: 'message_id',
      key: 'message_id',
      hideInSearch: true,
      render: (_, item: { message_id: string }) => {
        return <Text ellipsis={{ tooltip: item.message_id }}>{item.message_id}</Text>;
      },
    },
    {
      title: '记录时间',
      hideInSearch: true,
      dataIndex: 'created_at',
      key: 'created_at',
      render: (_, item: { created_at: string }) => {
        if (item.created_at) {
          return <span>{dayjs(new Date(item.created_at)).format('YYYY.MM.DD HH:mm:ss')}</span>;
        }
        return '';
      },
    },
  ];

  const requestTable = async (params: any) => {
    const { range_time, type, code, current, pageSize } = params;
    const payload: Record<string, any> = {
      type,
      code,
      page: current,
      page_size: pageSize,
    };
    if (type === '0') {
      delete payload.type;
    }
    if (range_time) {
      payload.start_time = dayjs(range_time[0]).startOf('day').format(DATE_FMT.DATE_TIME);
      payload.end_time = dayjs(range_time[1]).endOf('day').format(DATE_FMT.DATE_TIME);
    }

    const result = await queryMQTTLogRequest.run({
      ...payload,
      tenant_id: 1,
    });
    if (result.code === 200) {
      return {
        total: result.data.total,
        data: result.data.list,
      };
    } else {
      message.error(result.message || '请求失败');
    }
    return {
      total: 0,
      data: [],
    };
  };

  const paginationRef = useRef({
    current: 1,
    defaultPageSize: 20,
    total: 0,
  });
  return (
    <div className="flex-1 flex">
      <div className="flex-1">
        <XcTableNew
          ref={tableRef}
          loading={queryMQTTLogRequest.loading}
          columns={columns}
          pagination={paginationRef.current}
          request={requestTable}
          operator={null}
          rowSelection={null}
        />
      </div>
      {editorData ? (
        <JsonEditor editorData={editorData} onClose={closeEditorModal}></JsonEditor>
      ) : null}
    </div>
  );
};
