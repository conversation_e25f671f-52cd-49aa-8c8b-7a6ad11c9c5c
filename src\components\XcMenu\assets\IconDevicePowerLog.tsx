/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-24 00:06:23
 * @LastEditTime: 2025-02-13 22:01:16
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
const IconDevicePowerLog = (props: any) => {
  const { className, type, ...rest } = props;

  if (type === 'primary') {
    return (
      <svg
        className={`xc-svg ${className ?? ''}`}
        {...rest}
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <rect x="4" y="3" width="16" height="18" rx="3.5" fill="#0080FF" />
        <path d="M8 9L16 9" stroke="white" strokeWidth="1.5" strokeLinecap="round" />
        <path d="M8 13L12 13" stroke="white" strokeWidth="1.5" strokeLinecap="round" />
      </svg>
    );
  }

  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      {...rest}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        x="4.75"
        y="3.75"
        width="14.5"
        height="16.5"
        rx="2.75"
        stroke="#667085"
        strokeWidth="1.5"
      />
      <path d="M8 9L16 9" stroke="#667085" strokeWidth="1.5" strokeLinecap="round" />
      <path d="M8 13L12 13" stroke="#667085" strokeWidth="1.5" strokeLinecap="round" />
    </svg>
  );
};

export default IconDevicePowerLog;
