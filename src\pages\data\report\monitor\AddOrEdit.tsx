import { useEffect, useRef, useState } from 'react';
import { Button, message, Space, Tree } from 'antd';
import { useRequest } from '@umijs/max';
import { ProFormInstance, ProFormItem, ProFormText } from '@ant-design/pro-components';
import { NAME_REG_EXP } from '@/constants/regex';
import { IconClose } from '@/assets/svg';
import { compareArrayIdList } from '@/utils/common.util';
import XcAntdForm from '@/components/XcAntdForm';
import { XcDrawer } from '@/components/XcDrawer';
import { XcRangePicker } from '@/components/XcRangePicker';
import { IconDrop } from '@/components/Icon/IconDrop';
import { reportApi } from '@/api/report';
import dayjs from 'dayjs';
import { DATE_FMT } from '@/utils/time.util';
import DeviceTree from '@/pages/device/list/components/DeviceTree';
import { findNodeById } from '@/utils/tree.util';

export default function AddOrEdit(props: any) {
  const { modalType, currentItem, queryTableData, visible, onClose } = props;
  const [isFormChanged, setIsFormChanged] = useState(false);
  const [deviceIds, setDeviceIds] = useState<any>([]);
  const [drawerVisible, setDrawerVisible] = useState(visible);
  const [drawerShow, setDrawerShow] = useState(false);
  const formRef = useRef<ProFormInstance>(null);
  const modalNamePrefix = modalType === 1 ? '新增' : '修改';
  const [deviceTreeData, setDeviceTreeData] = useState([]);

  useEffect(() => {
    if (modalType === 1) {
      setIsFormChanged(true);
    } else {
      setIsFormChanged(false);
    }
  }, [currentItem, modalType]);

  useEffect(() => {
    if (modalType !== 1) {
      const ids = (currentItem.data_range || '').split(',').map((id: string) => +id);
      setDeviceIds([...ids]);
      currentItem.range_time = [dayjs(currentItem.start_time), dayjs(currentItem.end_time)];
      formRef.current?.setFieldsValue(currentItem);
    }
  }, [currentItem]);

  useEffect(() => {
    console.log('drawerShow ', deviceTreeData, deviceIds);
    if (drawerShow) {
      if (deviceTreeData?.length > 0) {
        const reportList: string[] = deviceIds.map((id: number) => {
          const node = findNodeById(deviceTreeData, id);
          return node?.point_manager || '';
        });
        const point_manager: string = reportList.filter((str) => str !== '').join('、');
        formRef.current?.setFieldValue('point_manager', point_manager);
      }
    }
  }, [deviceIds, drawerShow, deviceTreeData]);
  const onDrawerOpenChange = (open: boolean) => {
    if (open) {
      if (modalType !== 1) {
        formRef.current?.setFieldsValue(currentItem);
      } else {
        formRef.current?.setFieldsValue({
          range_time: [dayjs().add(-1, 'day'), dayjs()],
        });
      }
      setDrawerShow(true);
    } else {
      setDrawerShow(false);
      onClose();
    }
  };
  const closeDrawer = () => {
    setDrawerVisible(false);
  };
  /**
   * 新增请求接口
   */
  const reportAddRequest = useRequest(
    (data?: any, headers?: any) => ({
      method: 'POST',
      url: reportApi.postReport,
      data: { tenant_id: 1, report_type: 2, ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: null },
      throwOnError: true,
    },
  );
  /**
   * 编辑请求接口
   */
  const reportEditRequest = useRequest(
    (id, data?: any, headers?: any) => ({
      method: 'PUT',
      url: reportApi.putReport(id),
      data: { ...currentItem, ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: null },
      throwOnError: true,
    },
  );

  // 提交编辑表单
  const onFinishEdit = async (item: any) => {
    try {
      const result = await reportEditRequest.run(currentItem.id, {
        id: currentItem.id,
        ...item,
      });
      if (result.code === 200) {
        message.success(result.message || '修改成功。');
        await queryTableData();
        closeDrawer();
        return true;
      } else {
        message.error(result.message || '修改失败，请重试。');
      }
    } catch (error) {
      console.error('🚀 ~ onEditFinish ~ error:', error);
    }

    return false;
  };
  const onFinishAdd = async (item: any) => {
    try {
      const result = await reportAddRequest.run({
        ...item,
      });

      if (result.code === 200) {
        message.success('新增报表成功。');
        await queryTableData();
        closeDrawer();
        return true;
      } else {
        message.error(result.message || '新增失败，请重试。');
      }
    } catch (error) {
      console.error('🚀 ~ onAddFinish ~ error:', error);
    }

    return false;
  };
  const onSubmit = () => {
    const values = formRef.current?.getFieldsValue();
    // values.role_ids = values?.role_ids || []; //getQueryIdList(values?.role_ids);
    // values.orgs = values?.org_ids || []; //getQueryIdList(values?.org_ids);
    // 去掉不必要参数
    // delete values.role_ids;
    // delete values.org_ids;
    if (values.range_time) {
      const range_time = values.range_time;
      delete values.range_time;
      values.start_time = dayjs(range_time[0]).format(DATE_FMT.DATE_TIME);
      values.end_time = dayjs(range_time[1]).format(DATE_FMT.DATE_TIME);
    }
    if (modalType === 1) {
      return onFinishAdd(values);
    } else {
      // return onFinishEdit(values);
      closeDrawer();
      return Promise.resolve(true);
    }
  };

  const onValuesChange = (changedValues: any, allValues: any) => {
    if (modalType === 2) {
      const keys = Object.keys(allValues);
      const changed = keys.some((key) => {
        const newValue = allValues[key];
        const preValue = currentItem[key];
        let result = newValue !== preValue;
        if (Array.isArray(newValue) || Array.isArray(preValue)) {
          result = compareArrayIdList(newValue, preValue);
        }
        if (result) {
          // console.log(key, allValues[key], currentItem[key]);
        }
        return result;
      });
      setIsFormChanged(changed);
    }
  };

  const onDeviceChange = (checkedKeys: any) => {
    // const keys = [...checkedKeys.checked];
    const keys = [...checkedKeys];
    // console.log('onDeviceCheck', checkedKeys);
    formRef.current?.setFieldValue('data_range', keys);
    setDeviceIds(keys);
    onValuesChange(null, formRef.current?.getFieldsValue());
  };
  return (
    <>
      {/* <XcTableMask
        visible={drawerVisible}
        setVisible={closeDrawer}
        width={160}
        isFormChanged={isFormChanged}
      ></XcTableMask> */}
      <XcDrawer
        title={false}
        closable={false}
        mask={false}
        maskClosable={false}
        open={drawerVisible}
        width={600}
        destroyOnClose={false}
        afterOpenChange={onDrawerOpenChange}
        onClose={closeDrawer}
        isFormChanged={isFormChanged}
      >
        <div className="h-[70px] flex items-center px-[30px]">
          <div className="text-[20px] font-medium text-txt-main flex-1">{`${modalNamePrefix}报表`}</div>
          <Button
            type="text"
            className="text-[24px] text-txt-sub cursor-pointer"
            onClick={closeDrawer}
          >
            &times;
          </Button>
        </div>
        <div className="mt-0  border-line-split border-b-[1px] border-solid "></div>
        <div className="flex-1 xc-scrollbar-none p-[30px]">
          <XcAntdForm
            formRef={formRef}
            autoFocusFirstInput
            onFinish={onSubmit}
            onValuesChange={onValuesChange}
            submitter={{
              render: (_, dom) => null,
            }}
          >
            <ProFormText
              name="report_name"
              label={'报表名称'}
              required={true}
              disabled={modalType === 2}
              fieldProps={{
                maxLength: 20,
              }}
              rules={[
                {
                  required: true,
                  message: '报表名称不能为空',
                },
                {
                  max: 20,
                  message: '报表名称超过20个字',
                },
                {
                  pattern: NAME_REG_EXP,
                  message: '报表名称格式错误',
                },
              ]}
              placeholder={'请输入报表名称'}
            />
            <ProFormText hidden name="data_range"></ProFormText>
            <ProFormItem label="数据范围" required>
              <div className="xc-scrollbar-y xc-from-tree-box h-[430px] bg-white">
                <DeviceTree
                  defaultCheckedKeys={deviceIds}
                  disabled={modalType === 2}
                  onChange={onDeviceChange}
                  visible={drawerShow}
                  onDataLoad={(data) => {
                    setDeviceTreeData(data);
                  }}
                  deviceType="self"
                />
              </div>
            </ProFormItem>
            <ProFormItem label="监测时段" required name="range_time">
              <XcRangePicker
                suffixIcon={<IconDrop className="size-[24px]" />}
                disabled={modalType === 2}
                style={{
                  width: '100%',
                }}
                allowClear={{
                  clearIcon: <IconClose onClick={() => {}} />,
                }}
                showTime
              />
            </ProFormItem>
            <ProFormText
              name="point_manager"
              label={'监测负责人'}
              disabled
              fieldProps={{
                maxLength: 20,
              }}
              placeholder={''}
            />
          </XcAntdForm>
        </div>
        <div className="mb-0  border-line-split border-b-[1px] border-solid px-[30px]"></div>
        <div className="xc-form-footer">
          <Space>
            <Button className="xc-form-button-cancel" onClick={closeDrawer}>
              取消
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              className={`xc-form-button-submit  ${isFormChanged ? '' : 'opacity-30 cursor-default'}`}
              onClick={() => {
                formRef.current?.submit();
              }}
            >
              保存
            </Button>
          </Space>
        </div>
      </XcDrawer>
    </>
  );
}
