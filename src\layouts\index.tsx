import { XcMenu } from '@/components/XcMenu/XcMenu';
import { Nav } from '@/components/XcNav';
import Auth from '@/wrappers/Auth';
import { history, Outlet, useAppData, useLocation } from '@umijs/max';
import { ConfigProvider } from 'antd';
import { useEffect } from 'react';
import customTheme from './theme';
import { useLastRoute } from '@/hooks/useLastRoute';
import XxcHeader from '@/components/XxcHeader';
import XxcNav from '@/components/XxcNav';
import { LAYOUT_MODE, useLayoutStore } from '@/store';
import PasswordResetModal from '@/components/XxcPasswordResetModal/XxcPasswordResetModal';
import { STORAGE_KEY_INIT_PASSWD } from '@/constants/common';
import { useGlobalStore } from '@/store/globalStore';

export default function BasicLayout() {
  const layoutMode = useLayoutStore((state) => state.layoutMode);
  const { togglePasswordModalVisible } = useGlobalStore();
  useLastRoute();

  useEffect(() => {
    if (layoutMode === LAYOUT_MODE.ai) {
      const root = document.getElementById('root');
      root?.classList.add('is-ai-layout');
    }
  }, [layoutMode]);

  useEffect(() => {
    // const init = +(localStorage.getItem(STORAGE_KEY_INIT_PASSWD) || 0) || -10;
    // const now = new Date().getDate();
    // if (now - init > 7) {
    //   localStorage.setItem(STORAGE_KEY_INIT_PASSWD, now.toString());
    //   togglePasswordModalVisible(true);
    // }
  }, []);

  return (
    <ConfigProvider theme={customTheme}>
      <div className="w-screen">
        <XxcHeader />
        <div className="xxc-full-page xc-scrollbar-x xc-scrollbar-y">
          <XxcNav />
          <div className="flex-1 flex px-[16px] bg-[#f4f8fd] rounded-[24px] xc-scrollbar-x xc-scrollbar-y mb-[16px] mr-[16px]">
            <article className="flex-1 flex flex-col  min-w-[1000px]">
              <Auth>
                <Outlet />
              </Auth>
            </article>
          </div>
        </div>
        <PasswordResetModal />
      </div>
    </ConfigProvider>
  );
}
