/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-28 14:12:53
 * @LastEditTime: 2024-12-28 14:20:23
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { Checkbox } from 'antd';
import { useEffect, useState } from 'react';

interface IProps {
  className?: string;
  selectd?: number;
  onSelectAll: (checked: boolean) => void;
  children?: React.ReactNode;
}

export const CustomAllCheckbox = (props: IProps) => {
  const { className, children, onSelectAll: onChange, selectd } = props;
  const [checked, setChecked] = useState(false);
  const [indeterminate, setIndeterminate] = useState(false);

  const handleCheckAllChange = (e: any) => {
    setChecked(e.target.checked);
    onChange(e.target.checked);
  };

  useEffect(() => {
    if (selectd === 0 || selectd === 2) {
      setIndeterminate(false);
      setChecked(selectd > 0);
    } else {
      setIndeterminate(true);
    }
  }, [selectd]);

  return (
    <Checkbox
      className={className}
      onChange={handleCheckAllChange}
      checked={checked}
      indeterminate={indeterminate}
    >
      {children}
    </Checkbox>
  );
};
