import { COLUMN_RENDER_TYPE, IColumns } from '@/components/XcTable/interface/search.interface';
import { request, useRequest } from '@umijs/max';
import { Button, Image, message, Modal, Table, Typography } from 'antd';
import { Children, lazy, memo, Suspense, useEffect, useRef, useState } from 'react';
import dayjs from 'dayjs';
import { DATE_FMT } from '@/utils/time.util';
import { logApi } from '@/api/log';
import { permissionApi } from '@/api/permission';
import { XcTableNew } from '@/components/XcTable/XcTableNew';
import { DEFAULT_EXPERT_USER_NAME } from '@/constants/common';
import XcAntdText from '@/components/XcAntdText';
import { XcStyledTable } from '@/components/XcAntdTable/XcAntdTable';
import { getAllMosqutio } from '@/utils/mosqutio.util';
import { useMemoizedFn } from 'ahooks';
import JsonEditor from '@/components/JsonEditor/JsonEditor';
import { AUDIT_VERIFY_DATA_STATUS } from '../data/audit/constant';

const OptimizedChild = memo((props: any) => {
  const { record, showJsonModal, mosqutioList } = props;
  const childrenColumns = [
    {
      title: '日志编号',
      dataIndex: 'id',
      key: 'id',
      width: 100,
    },
    {
      title: '请求数据',
      dataIndex: 'request_data',
      key: 'request_data',
      render: (value: string, record: any) => {
        return (
          <div
            className="text-one-row cursor-pointer"
            onClick={() =>
              showJsonModal({
                json: value,
                title: `日志编号-${record.id} 请求数据：`,
              })
            }
          >
            {value}
          </div>
        );
      },
    },
    {
      title: '响应数据',
      dataIndex: 'response_data',
      key: 'response_data',
      render: (value: string) => {
        return (
          <div
            className="text-one-row cursor-pointer"
            onClick={() =>
              showJsonModal({
                json: value,
                title: `日志编号-${record.id} 响应数据：`,
              })
            }
          >
            {value}
          </div>
        );
      },
    },
    {
      title: '标签信息',
      dataIndex: 'remark',
      key: 'remark',
      renderType: COLUMN_RENDER_TYPE.text_ellipsis,
    },
    // {
    //   title: '蚊媒',
    //   dataIndex: 'species_type',
    //   key: 'species_type',
    //   render: (value: string) => {
    //     const mosInfo = mosqutioList.find((mos: any) => {
    //       return (value || '').toLocaleLowerCase() === mos.code.toLocaleLowerCase();
    //     }) || { name: '' };
    //     return <XcAntdText>{mosInfo?.name || ''}</XcAntdText>;
    //   },
    // },
    {
      title: '阶段名称',
      dataIndex: 'stage_name',
      key: 'stage_name',
      renderType: COLUMN_RENDER_TYPE.text_ellipsis,
    },
    // {
    //   title: '状态',
    //   dataIndex: 'status_name',
    //   key: 'status_name',
    //   renderType: COLUMN_RENDER_TYPE.text_ellipsis,
    // },
    {
      title: '记录时间',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 160,
      renderType: COLUMN_RENDER_TYPE.text_ellipsis,
    },
  ];
  return (
    <XcStyledTable
      columns={childrenColumns}
      dataSource={record.subList || []}
      pagination={false}
      tableLayout="fixed"
    />
  );
});

export default () => {
  const [isPreviewVisible, setIsPreviewVisible] = useState(false);
  const [previewSrc, setPreviewSrc] = useState('');
  const [mosqutioList, setMosqutioList] = useState<any[]>([]);
  const logListRequest = useRequest(
    (data?: any, headers?: any) => ({
      url: logApi.getAiVerify,
      method: 'GET',
      params: { ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: { total: 0, page: 1, pageSize: 50, list: [] } },
      throwOnError: true,
    },
  );

  const requestTable = async (params: any) => {
    const { range_time, current, pageSize, ...others } = params;
    const payload: Record<string, any> = {
      page: current,
      page_size: pageSize,
    };
    if (others.menu === 'all') {
      delete others.menu;
    }
    if (others.action === 'all') {
      delete others.action;
    }
    if (range_time) {
      payload.start_time = dayjs(range_time[0]).startOf('day').format(DATE_FMT.DAY);
      payload.end_time = dayjs(range_time[1]).endOf('day').format(DATE_FMT.DAY);
    } else {
      // return;
    }
    const queryPayload = {
      ...others,
      ...payload,
      tenant_id: 1,
    };
    const result = await logListRequest.run(queryPayload);
    const list = result.data.list || [];
    const newList = list.map((item: any) => {
      return {
        ...item,
        subList: item.children || [],
        children: undefined,
      };
    });
    if (result.code === 200) {
      return {
        total: result.data.total || 0,
        data: newList,
      };
    } else {
      message.error(result.message || '请求失败');
    }

    return {
      total: 0,
      data: [],
    };
  };

  const hidePicModal = () => {
    setPreviewSrc('');
    setIsPreviewVisible(false);
  };
  const showPicModal = (src: string) => {
    setIsPreviewVisible(true);
    setPreviewSrc(src);
  };
  const onVisibleChange = (value: boolean, prevValue: boolean): void => {
    if (!value) {
      hidePicModal();
    }
  };

  const [editorData, setEditorData] = useState<Record<string, any> | null>(null);

  const closeEditorModal = () => {
    setEditorData(null);
  };
  const showJsonModal = useMemoizedFn((data: Record<string, any>) => {
    setEditorData(data);
  });

  const columns: IColumns[] = [
    {
      title: '创建时间',
      key: 'range_time',
      initialValue: [dayjs().subtract(30, 'day'), dayjs()],
      maxValue: dayjs(),
      minValue: dayjs('2024-12-01'),
      rangeLimit: [18, 'month'],
      hideInTable: true,
      valueType: 'rangePicker',
    },
    {
      title: '设备编号',
      width: 320,
      key: 'code',
      hideInTable: true,
      placeholder: '搜索数据编号/设备名称',
      valueType: 'input',
    },
    {
      title: '数据编号',
      dataIndex: 'id',
      hideInSearch: true,
      key: 'id',
    },
    {
      title: '设备ID',
      dataIndex: 'device_code',
      key: 'device_code',
      hideInSearch: true,
      renderType: COLUMN_RENDER_TYPE.text_ellipsis,
    },
    {
      title: '设备名称',
      dataIndex: 'device_name',
      key: 'device_name',
      hideInSearch: true,
      renderType: COLUMN_RENDER_TYPE.text_ellipsis,
    },
    {
      title: '监测点位置',
      dataIndex: 'monitor_location',
      key: 'monitor_location',
      hideInSearch: true,
      width: 300,
      renderType: COLUMN_RENDER_TYPE.text_ellipsis,
    },
    {
      title: '生境类型',
      dataIndex: 'area_type',
      key: 'device_name',
      hideInSearch: true,
      renderType: COLUMN_RENDER_TYPE.text_ellipsis,
    },
    {
      title: '验证数据',
      dataIndex: 'ai_result',
      key: 'ai_result',
      hideInSearch: true,
      render: (value) => {
        return (value || '').split(',').map((txt: string, index: number) => {
          return <div key={index}>{txt}</div>;
        });
      },
    },
    {
      title: '验证切图',
      dataIndex: 'image_url',
      key: 'image_url',
      hideInSearch: true,
      render: (value: any) => {
        return (
          <Button
            type="link"
            onClick={() => showPicModal(value)}
            className="text-txt-blue"
            style={{ padding: 0 }}
          >
            查看切图
          </Button>
        );
      },
    },
    {
      title: '验证专家',
      dataIndex: 'wendu',
      key: 'wendu',
      hideInSearch: true,
      render: (auditor_name: any) => {
        return <XcAntdText>{auditor_name || DEFAULT_EXPERT_USER_NAME}</XcAntdText>;
      },
    },
    {
      title: '验证状态',
      dataIndex: 'status',
      key: 'status',
      hideInSearch: true,
      render: (status: any, item: any) => {
        return (
          <XcAntdText>
            {item.status === AUDIT_VERIFY_DATA_STATUS.AI验证通过
              ? 'AI验证通过'
              : item.status === AUDIT_VERIFY_DATA_STATUS.专家已验证
                ? '专家已验证'
                : item.status === AUDIT_VERIFY_DATA_STATUS.待专家验证
                  ? '待专家验证'
                  : 'AI验证中'}
          </XcAntdText>
        );
      },
    },
    {
      title: '验证时间',
      dataIndex: 'audit_time',
      key: 'audit_time',
      hideInSearch: true,
      width: 160,
      renderType: COLUMN_RENDER_TYPE.text_ellipsis,
    },
    {
      title: '记录时间',
      dataIndex: 'created_at',
      key: 'created_at',
      hideInSearch: true,
      width: 160,
      renderType: COLUMN_RENDER_TYPE.text_ellipsis,
    },
  ];

  useEffect(() => {
    const getData = async () => {
      const result = await request(permissionApi.getDataPermissionList, {
        method: 'GET',
        params: {
          tenant_id: 1,
          type: 'mosquito',
          // level: 3,
        },
      });
      if (Array.isArray(result?.data?.list) && result?.data?.list[0]) {
        const list = result?.data?.list[0].children || [];
        const mqlist: any = getAllMosqutio(list);
        setMosqutioList(mqlist);
      }
    };
    getData();
  }, []);
  const paginationRef = useRef({
    current: 1,
    defaultPageSize: 10,
    total: 0,
  });
  return (
    <div className="flex-1 flex ">
      <XcTableNew
        loading={logListRequest.loading}
        columns={columns}
        rowKey="id"
        request={requestTable}
        pagination={paginationRef.current}
        virtual={false}
        extend={null}
        batchRender={() => null}
        operator={null}
        rowSelection={null}
        expandable={{
          columnWidth: 48,
          indentSize: 48,
          expandedRowRender: (record: any) => (
            <OptimizedChild
              record={record}
              showJsonModal={showJsonModal}
              mosqutioList={mosqutioList}
            />
          ),
        }}
      />
      {isPreviewVisible ? (
        <Image
          src={previewSrc}
          style={{ width: '100%', display: 'none' }}
          preview={{
            onVisibleChange: onVisibleChange,
            toolbarRender: () => null,
            visible: true,
          }}
        />
      ) : null}
      {editorData ? (
        <JsonEditor editorData={editorData} onClose={closeEditorModal}></JsonEditor>
      ) : null}
    </div>
  );
};
