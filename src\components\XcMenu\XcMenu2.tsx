/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-02-09 02:13:23
 * @LastEditTime: 2025-02-12 16:57:18
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { styled } from '@umijs/max';
import { Typography } from 'antd';
import { useEffect, useState } from 'react';
import { px2font, px2vh, px2vw } from '../LargeScreen/vw/vw';
import { IconArrowDown } from './IconArrowDown';

const { Text } = Typography;
export interface XcMenuItem {
  key: string;
  label: string;
  children?: XcMenuItem[];
  icon?: React.ReactNode;
}

export interface XcMenuProps {
  items: XcMenuItem[];
  onClickItem: (item: any) => void;
  defaultExpandedKeys?: string[];
  defaultSelectedKey?: string;
}

const StyledXcMenus = styled.div`
  width: 100%;
  height: ${() => px2vh(512)};
  padding: ${() => `0 ${px2vw(20)}`};
  overflow-y: auto;
  overflow-x: hidden;
  font-family: 'PingFang SC';

  .menu-item-container {
    position: relative;
    color: #fff;
  }
`;
const StyledItem = styled.div<{ $level: number }>`
  display: flex;
  height: ${() => px2vh(32)};
  padding-right: ${() => px2vw(20)};
  padding-left: ${(props) => props.$level * 20 + 14}px !important;
  margin-bottom: 7px;
  align-items: center;
  border-radius: ${() => px2font(6)};
  color: #fff;
  font-family: 'PingFang SC';
  font-size: ${() => px2font(14)};
  font-style: normal;
  font-weight: 600;
  line-height: normal;
  cursor: pointer;
  position: relative;
  transition: all 0.3s ease;
  &:hover {
    border-radius: 6px;
  }
  &.selected {
    border-radius: 6px;
    background-color: #2953ff !important;
    color: white;
    svg {
      g {
        opacity: 1;
      }
    }
    span {
      font-weight: 600;
    }
  }
`;
const StyledLabel = styled(Text)<{ $level: number }>`
  font-family: 'PingFang SC';
  font-size: ${() => px2font(14)};
  color: #ffffff;
  font-weight: 400;
  width: 100%;
  &:hover {
    font-weight: 600;
  }
`;
const StyledIcon = styled(IconArrowDown)`
  transition: transform 0.3s ease;
  width: ${() => px2font(16)};
  height: ${() => px2font(16)};
  margin-right: ${() => px2vw(6)};

  &:hover {
    g {
      opacity: 1;
    }
  }
  &.expanded {
    transform: rotate(180deg);
  }
`;
const StyledSubMenu = styled.div`
  max-height: 0;
  overflow: hidden;
  transition: max-height 0.3s ease-out;

  &.open {
    max-height: min-content; /* 根据实际内容高度调整 */
  }
`;

// 查找节点及其所有父节点
const findNodeHierarchy = (
  items: XcMenuItem[],
  targetId: string,
): { node?: XcMenuItem; parentKeys: string[] } => {
  const result = { node: undefined, parentKeys: [] } as any;
  const traverse = (nodes: XcMenuItem[], path: string[]): boolean => {
    return nodes.some((node) => {
      if (node.key === targetId) {
        result.node = node;
        result.parentKeys = path;
        return true;
      }
      if (node.children) {
        return traverse(node.children, [...path, node.key]);
      }
      return false;
    });
  };

  traverse(items, []);
  return result;
};

export const XcMenu2 = ({
  items,
  defaultExpandedKeys = [],
  onClickItem,
  defaultSelectedKey,
}: XcMenuProps) => {
  const [expanded, setExpanded] = useState<Record<string, boolean>>();
  const [selectedKey, setSelectedKey] = useState<string | null>(defaultSelectedKey || null);

  useEffect(() => {
    if (defaultSelectedKey) {
      setSelectedKey(defaultSelectedKey);
    }
  }, [defaultSelectedKey]);
  useEffect(() => {
    const initialExpanded: Record<string, boolean> = {};
    defaultExpandedKeys.forEach((key) => {
      const { node, parentKeys } = findNodeHierarchy(items, key);
      // 展开所有父级
      parentKeys.forEach((pkey) => (initialExpanded[pkey] = true));
      // 展开当前节点（如果有子项）
      if (node?.children) initialExpanded[key] = true;
    });
    // 处理默认选中项的父级展开
    if (defaultSelectedKey) {
      const { parentKeys } = findNodeHierarchy(items, defaultSelectedKey);
      parentKeys.forEach((pkey) => (initialExpanded[pkey] = true));
    }
    setExpanded(initialExpanded);
  }, [defaultExpandedKeys]);

  const handleItemClick = (item: XcMenuItem) => {
    setSelectedKey(item.key);

    onClickItem(item);
  };

  // 处理箭头点击（展开/收起）
  const handleArrowClick = (e: React.MouseEvent, item: any) => {
    e.stopPropagation();
    if (!item.children) return;
    setExpanded((prev) => ({ ...prev, [item.key]: !prev[item.key] }));
  };

  const shouldShowSelected = (item: XcMenuItem) => {
    // 选中即显示
    return selectedKey === item.key;
  };

  const renderMenuItems = (items: XcMenuItem[], level = 0) => {
    return items.map((item) => (
      <div key={item.key} className="menu-item-container">
        <StyledItem className={`${shouldShowSelected(item) ? 'selected' : ''}`} $level={level}>
          {item.children && (
            <StyledIcon
              color="#fff"
              className={`${expanded?.[item.key] ? 'expanded' : ''}`}
              onClick={(e: any) => handleArrowClick(e, item)}
            />
          )}
          <StyledLabel
            onClick={() => handleItemClick(item)}
            $level={level}
            ellipsis={{
              tooltip: {
                title: item.label,
                styles: {
                  body: {
                    display: 'flex',
                    alignItems: 'center',
                    fontSize: px2font(16),
                  },
                },
              },
            }}
          >
            {item.label}
          </StyledLabel>
        </StyledItem>

        {item.children && (
          <StyledSubMenu className={`${expanded?.[item.key] ? 'open' : ''}`}>
            {renderMenuItems(item.children, level + 1)}
          </StyledSubMenu>
        )}
      </div>
    ));
  };

  return <StyledXcMenus className="xc-scrollbar">{renderMenuItems(items)}</StyledXcMenus>;
};
