const path = require('path');

module.exports = (ctx) => {
  // const isVwFolder = ctx.file && ctx.file.indexOf(path.join(__dirname, 'src', 'css', 'vw')) !== -1;
  // console.log('1111111111111111111', ctx.file);
  const isVwFolder = true;
  return {
    plugins: {
      'postcss-px-to-viewport': isVwFolder
        ? {
            viewportWidth: 750,
            unitPrecision: 5,
            viewportUnit: 'vw',
            selectorBlackList: [],
            minPixelValue: 1,
            mediaQuery: false,
          }
        : false,
    },
  };
};
