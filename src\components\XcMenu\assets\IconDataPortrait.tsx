/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-10 23:29:35
 * @LastEditTime: 2025-02-13 21:59:59
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
const IconDataPortrait = (props: any) => {
  const { className, type, ...rest } = props;

  if (type === 'primary') {
    return (
      <svg
        className={`xc-svg ${className ?? ''}`}
        {...rest}
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M17.8 4H6.2C4.43269 4 3 5.43269 3 7.2V15.3C3 17.0673 4.43269 18.5 6.2 18.5H8L11.9253 14.0841C11.965 14.0393 12.035 14.0393 12.0747 14.0841L16 18.5H17.8C19.5673 18.5 21 17.0673 21 15.3V7.2C21 5.43269 19.5673 4 17.8 4Z"
          fill="#0080FF"
        />
        <path
          d="M15.3489 19.9374C15.6111 20.2647 15.3781 20.75 14.9587 20.75H9.03931C8.62021 20.75 8.38707 20.2653 8.64866 19.9379L11.6043 16.2383C11.8043 15.988 12.1849 15.9877 12.3852 16.2378L15.3489 19.9374Z"
          fill="#0080FF"
        />
        <path d="M15 10L15 14" stroke="white" strokeWidth="1.5" strokeLinecap="round" />
        <path d="M12 8L12 13" stroke="white" strokeWidth="1.5" strokeLinecap="round" />
        <path d="M9 11L9 14" stroke="white" strokeWidth="1.5" strokeLinecap="round" />
      </svg>
    );
  }

  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      {...rest}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M11.9952 16.9506L14.4381 20H9.55902L11.9952 16.9506ZM12.1903 16.7064C12.1902 16.7065 12.1901 16.7066 12.1901 16.7067L12.1903 16.7064L11.995 16.5504L12.1903 16.7064Z"
        stroke="#667085"
        strokeWidth="1.5"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M2.99988 7.21856C2.99988 5.441 4.45495 4 6.24988 4H17.7499C19.5448 4 20.9999 5.441 20.9999 7.21856V15.2814C20.9999 17.059 19.5448 18.5 17.7499 18.5H16.5311C16.1169 18.5 15.7811 18.1675 15.7811 17.7573C15.7811 17.347 16.1169 17.0145 16.5311 17.0145H17.7499C18.7164 17.0145 19.4999 16.2386 19.4999 15.2814V7.21856C19.4999 6.26141 18.7164 5.48549 17.7499 5.48549H6.24988C5.28338 5.48549 4.49988 6.26141 4.49988 7.21856V15.2814C4.49988 16.2386 5.28338 17.0145 6.24988 17.0145H7.46859C7.88281 17.0145 8.21859 17.347 8.21859 17.7573C8.21859 18.1675 7.88281 18.5 7.46859 18.5H6.24988C4.45495 18.5 2.99988 17.059 2.99988 15.2814V7.21856Z"
        fill="#667085"
      />
      <path d="M15 10L14.9999 14" stroke="#667085" strokeWidth="1.5" strokeLinecap="round" />
      <path d="M12 8L11.9999 13" stroke="#667085" strokeWidth="1.5" strokeLinecap="round" />
      <path d="M9 11L8.99988 14" stroke="#667085" strokeWidth="1.5" strokeLinecap="round" />
    </svg>
  );
};

export default IconDataPortrait;
