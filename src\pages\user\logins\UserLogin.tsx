import { userApi } from '@/api/user';
import imgAvatar from '@/assets/user/img_avatar.jpg';
import { ReactComponent as Logo } from '@/assets/logo.svg';
import { ResponseErrorInfo } from '@/requestErrorConfig';
import { ProForm, ProFormText } from '@ant-design/pro-components';
import { history, useModel, useRequest } from '@umijs/max';
import { useDebounceFn } from 'ahooks';
// @ts-ignore
import CryptoJS from 'crypto-js';
import { Button, message } from 'antd';
import { getUserPermission } from '@/utils/permission';
import { getFirstRoutePath } from '@/utils/common.util';
import classNames from 'classnames';
import { ReactComponent as LockOutlined } from '@/assets/user/icon-lock.svg';
import { ReactComponent as MobileOutlined } from '@/assets/user/icon-mobile.svg';
import { ReactComponent as UserOutlined } from '@/assets/user/icon-people.svg';
import { useState } from 'react';
import PasswordResetModal from '@/components/XxcPasswordResetModal/XxcPasswordResetModal';
import React from 'react';
import { useForm } from 'antd/es/form/Form';
import { useGlobalStore } from '@/store/globalStore';
import { STORAGE_KEY_INIT_PASSWD } from '@/constants/common';

import './UserLogin.css';
enum LoginType {
  phone = 'phone',
  account = 'account',
}
export default () => {
  const { setPersist } = useModel('persist');
  const { setInitialState } = useModel('@@initialState');
  const [loginType, setLoginType] = useState(LoginType.account);
  const { togglePasswordModalVisible } = useGlobalStore();
  const [form] = useForm();
  const [isSubmitEnable, setIsSubmitEnable] = useState(false);

  const loginRequest = useRequest(
    (data?: any, headers?: any) => ({
      method: 'POST',
      url: userApi.login,
      data: { ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      throwOnError: true,
    },
  );

  const onFinish = async (values: any) => {
    const { username, password } = values;
    const pwd = CryptoJS.SHA256(password).toString();
    try {
      const loginResult = await loginRequest.run({ username, password: pwd, tenant_id: 1 });
      const { code, message: msg, data } = loginResult;

      if (code !== 200) {
        message.error(msg, 3);
        return;
      }
      // 模拟密码重置
      if (password === 'xxc@2025') {
        const init = +(localStorage.getItem(STORAGE_KEY_INIT_PASSWD) || 0) || -10;
        const now = new Date().getDate();
        // 7天内不提示
        if (now - init > 7) {
          localStorage.setItem(STORAGE_KEY_INIT_PASSWD, now.toString());
          togglePasswordModalVisible(true);
        }
        // localStorage.setItem(STORAGE_KEY_INIT_PASSWD, now.toString());
      }
      const { access_token, token_type, user_info } = data;

      setPersist('token', { access_token, token_type });
      setPersist('user', {
        avatar_img: imgAvatar,
        name: user_info.username,
        ...user_info,
      });
      // TODO 设置用户信息
      setInitialState((s: any) => ({
        ...s,
        user: {
          avatar_img: imgAvatar,
          name: user_info.username,
          ...user_info,
        },
      }));

      setTimeout(() => {
        getUserPermission(true);
        const path = getFirstRoutePath();
        history.push(path || '/welcome');
      }, 200);
    } catch (error: any) {
      message.error((error as ResponseErrorInfo).info.message);
    }
  };
  const { run } = useDebounceFn(onFinish, {
    wait: 3000,
    leading: true,
  });

  const goHome = () => {};

  const onValuesChange = () => {
    const result = form.getFieldsValue();
    if (result.username && result.password) {
      setIsSubmitEnable(true);
    } else {
      setIsSubmitEnable(false);
    }
  };

  return (
    <div className="Login" role="main">
      <Logo onClick={goHome} className="Login-logo" />
      <div className="LoginForm">
        <div className="LoginForm-name"></div>
        <div className="LoginForm-desc">为实现病媒可控而创新!</div>
        <div className="LoginForm-tabs">
          <div
            onClick={() => setLoginType(LoginType.account)}
            className={classNames('LoginForm-tabItem ', {
              'is-active': loginType === LoginType.account,
            })}
          >
            账号登录
          </div>
          <div
            onClick={() => setLoginType(LoginType.phone)}
            className={classNames('LoginForm-tabItem ', {
              'is-active': loginType === LoginType.phone,
            })}
          >
            手机登录
          </div>
        </div>
        <ProForm
          onFinish={run}
          onValuesChange={onValuesChange}
          onBlur={onValuesChange}
          isKeyPressSubmit
          className="LoginFormBox"
          form={form}
          submitter={{
            render: (props) => {
              return [
                <Button
                  className={'LoginForm-submit ' + (isSubmitEnable ? 'is-active' : '')}
                  block
                  type="primary"
                  tabIndex={3}
                  key="submit"
                  onClick={() => {
                    if (isSubmitEnable) {
                      props.form?.submit?.();
                    }
                  }}
                >
                  登录
                </Button>,
              ];
            },
          }}
        >
          {loginType === LoginType.account ? (
            <React.Fragment>
              <ProFormText
                fieldProps={{
                  autoComplete: 'new-password',
                  tabIndex: 1,
                  prefix: <UserOutlined className="text-[#C4CDD8]" />,
                  className: 'LoginForm-account',
                  maxLength: 20,
                  onInput: () => {
                    onValuesChange();
                  },
                  onKeyDown: (e: any) => {
                    if (e.keyCode === 32) {
                      e.preventDefault();
                    }
                  },
                }}
                name="username"
                label=""
                required={false}
                validateTrigger="submit"
                rules={[
                  { required: true, message: '用户名不能为空' },
                  { type: 'string', message: '用户名不能为空' },
                  { max: 20, message: '仅支持输入20个字' },
                ]}
                placeholder="请输入用户名"
              />
              <ProFormText.Password
                fieldProps={{
                  autoComplete: 'new-password',
                  tabIndex: 2,
                  prefix: <LockOutlined className="text-[#C4CDD8]" />,
                  className: 'LoginForm-password',
                  onInput: () => {
                    onValuesChange();
                  },
                  onKeyDown: (e: any) => {
                    if (e.keyCode === 32) {
                      e.preventDefault();
                    }
                  },
                }}
                required={false}
                name="password"
                label=""
                validateTrigger="submit"
                rules={[
                  { required: true, message: '密码不能为空' },
                  { type: 'string', message: '账号不能为空' },
                  { max: 50, message: '仅支持输入20个字' },
                ]}
                placeholder="请输入密码"
              />
            </React.Fragment>
          ) : (
            <React.Fragment>
              <ProFormText
                fieldProps={{
                  autoComplete: 'new-password',
                  tabIndex: 1,
                  prefix: <MobileOutlined className="text-[#C4CDD8]" />,
                  className: 'LoginForm-mobile',
                  maxLength: 20,
                  onInput: () => {
                    onValuesChange();
                  },
                  onKeyDown: (e: any) => {
                    if (e.keyCode === 32) {
                      e.preventDefault();
                    }
                  },
                }}
                name="mobile"
                label=""
                required={false}
                validateTrigger="submit"
                rules={[
                  { required: true, message: '手机号不能为空' },
                  { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确' },
                ]}
                placeholder="请输入手机号"
              />
              <ProFormText
                fieldProps={{
                  autoComplete: 'new-password',
                  tabIndex: 1,
                  prefix: <LockOutlined className="text-[#C4CDD8]" />,
                  className: 'LoginForm-captcha',
                  suffix: <Button className="LoginForm-captchaBtn">获取验证码</Button>,
                  maxLength: 20,
                  onKeyDown: (e: any) => {
                    if (e.keyCode === 32) {
                      e.preventDefault();
                    }
                  },
                }}
                name="captcha"
                label=""
                required={false}
                validateTrigger="submit"
                rules={[
                  { required: true, message: '验证码不能为空' },
                  { max: 6, message: '仅支持输入6个字' },
                ]}
                placeholder="请输入验证码"
              />
            </React.Fragment>
          )}
        </ProForm>
      </div>
      <PasswordResetModal />
    </div>
  );
};
