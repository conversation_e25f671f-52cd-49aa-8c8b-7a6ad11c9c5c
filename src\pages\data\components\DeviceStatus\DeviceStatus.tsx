import { px2font, px2vh, px2vw } from '@/components/LargeScreen/vw/vw';
import { styled } from '@umijs/max';
import { IconDot } from '../../../../components/Icon/IconDot';

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-24 11:47:10
 * @LastEditTime: 2025-01-26 23:54:59
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  status: 'success' | 'default' | 'error' | 'warning' | 'processing';
  text: string;
  dot?: boolean;
  successDotColor?: string;
  defaultDotColor?: string;
}

const StyedDeviceStatus = styled.div`
  gap: ${() => px2vw(6)};
  padding: ${() => px2vh(3)} ${() => px2vw(10)};
  border-radius: ${() => px2font(16)};
  .xc-svg {
    width: ${() => px2vw(8)};
    height: ${() => px2vw(8)};
  }
  .span {
    text-align: center;
    font-family: 'PingFang SC';
    font-size: ${() => px2font(14)};
    font-style: normal;
    font-weight: 400;
    line-height: ${() => px2font(18)};
  }
`;

export const DeviceStatus = (props: IProps) => {
  const {
    dot = true,
    status,
    text,
    successDotColor = '#07C777',
    defaultDotColor = '#667085',
  } = props;

  if (status === 'success') {
    return (
      <StyedDeviceStatus className="flex items-center bg-[#ECFDF3] rounded-[16px]">
        {dot && <IconDot color={successDotColor} />}
        <span className={`text-[#037847]`}>{text}</span>
      </StyedDeviceStatus>
    );
  }
  return (
    <StyedDeviceStatus className="flex items-center bg-[#F4F4F6] rounded-[16px]">
      {dot && <IconDot color={defaultDotColor} />}
      <span className={`text-txt-main`}>{text}</span>
    </StyedDeviceStatus>
  );
};
