import { commonStyles } from '@/constants/commonStyles';
import { IconEllipse } from '../Icon/IconEllipse';

/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-25 08:11:52
 * @LastEditTime: 2025-02-06 19:58:02
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
interface IProps {
  title: string;
  icon: React.ReactNode;
  list: any[];
}

export const XcList = (props: IProps) => {
  const { title, icon, list } = props;
  return (
    <div className="flex flex-col gap-[10px]">
      <div className="flex items-center gap-[10px]">
        {icon}
        <span className={`${commonStyles.baseTextColor} ${commonStyles.mediumText16} `}>
          {title}
        </span>
      </div>
      {list.map((item, index) => {
        return (
          <div key={index} className="flex items-center gap-[10px] px-[7px]">
            <IconEllipse className="shrink-0 size-[10px]" />
            <span className={`${commonStyles.normalText14} ${commonStyles.baseTextColor}`}>
              {item}
            </span>
          </div>
        );
      })}
    </div>
  );
};
