/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-23 23:39:34
 * @LastEditTime: 2025-01-30 16:58:11
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { commonStyles } from '@/constants/commonStyles';
import { Checkbox } from 'antd';
import { memo, useEffect, useState } from 'react';

interface IPropsItem {
  item: any;
  columns: any[];
  selectAll?: number;
  onSelectItem?: (checked: boolean, item: any) => void;
}

export const XcTableBodyItem = memo((props: IPropsItem) => {
  const { item, columns, onSelectItem, selectAll } = props;
  const [checked, setChecked] = useState(false);
  const [link, setLink] = useState(false);

  useEffect(() => {
    if (selectAll === 0 || selectAll === 2) {
      setChecked(selectAll > 0);
    }
  }, [selectAll]);

  const onChange = (e: any, item: any) => {
    setChecked(e.target.checked);
    if (onSelectItem) {
      onSelectItem(e.target.checked, item);
    }
  };

  return (
    <tr
      onMouseEnter={() => setLink(true)}
      onMouseLeave={() => setLink(false)}
      className={`${commonStyles.tableItemHighlight}`}
    >
      <td className="xc-table-checkbox">
        <Checkbox
          className="size-[18px]"
          checked={checked}
          onChange={(e) => {
            onChange(e, item);
          }}
        />
      </td>
      {columns.map((column) => {
        return (
          <td
            key={item.id + '_' + column.title}
            className={`whitespace-nowrap text-[14px] h-[44px] ${column.className ?? ''} ${
              link && column.link ? `!text-txt-blue font-medium` : 'text-txt-main font-normal'
            }`}
            style={{
              textAlign: column.align ?? 'left',
            }}
          >
            {column.render ? column.render({}, item) : (item[column.dataIndex] ?? '')}
          </td>
        );
      })}
    </tr>
  );
});
