/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-10 23:24:08
 * @LastEditTime: 2025-01-14 22:29:59
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
const IconDataMonitorPrimary = (props: any) => {
  const { className, ...rest } = props;
  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      {...rest}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect x="3" y="4" width="18" height="16" rx="4" fill="#0080FF" />
      <path
        d="M4 14.3333H7.88559L9.09438 11.1857C9.21937 10.8603 9.67188 10.8394 9.82637 11.1519L11.7953 15.1348C11.9531 15.4542 12.4183 15.4234 12.5327 15.0859L14.5489 9.13597C14.6722 8.77195 15.1879 8.77444 15.3078 9.13962L17.0121 14.3333H20"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
    </svg>
  );
};

export default IconDataMonitorPrimary;
