/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-23 21:08:59
 * @LastEditTime: 2025-01-11 13:34:35
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
module.exports = {
  content: [
    './src/pages/**/*.tsx',
    './src/constants/**/*.ts',
    './src/components/**/*.tsx',
    './src/layouts/**/*.tsx',
  ],
  theme: {
    extend: {
      colors: {
        'txt-blue': 'var(--txt-blue)', // 蓝色
        'txt-white': 'var(--txt-white)', // 白色
        'txt-main': 'var(--txt-main)', // 主色
        'txt-sub': 'var(--txt-sub)', // 次色
        'txt-third': 'var(--txt-third)', // 辅助色
        'bgc-global': 'var(--bgc-global)', // 背景 全局
        'bgc-normal': 'var(--bgc-normal)', // 背景通用
        'bgc-detail': 'var(--bgc-detail)', // 背景详情
        'bgc-head': 'var(--bgc-head)', // 背景表头
        'line-split': 'var(--line-split)', // 分割线
        'line-border': 'var(--line-border)', // 边框
        'icon-normal': 'var(--icon-normal)', // 图标通用色,
        'btn-blue': 'var(--btn-blue)', // 按钮蓝色
        'btn-white': 'var(--btn-white)', // 按钮白色
        'sys-red': 'var(--sys-red)', // 红色
        'sys-purple': 'var(--sys-purple)', // 紫色
        'sys-green': 'var(--sys-green)', // 绿色
        'sys-yellow': 'var(--sys-yellow)', // 黄色
        menu: 'var(--menu)', // 菜单,
        'menu-active': 'var(--menu-active)', // 菜单 - 选中
        'box-input': 'var(--box-input)', //  框 - 输入
        'box-robot': 'var(--box-robot)', //  框 - 星小尘
        'box-menu': 'var(--box-menu)', // 框 - 菜单
      },
    },
  },
};
