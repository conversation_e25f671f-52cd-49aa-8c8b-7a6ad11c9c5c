import { orgApi } from '@/api/org';
import {
  ProFormTreeSelect,
  ProFormTreeSelectProps,
  RequestOptionsType,
} from '@ant-design/pro-components';
import { request } from '@umijs/max';
import { useCallback, useEffect, useState } from 'react';

export default function XcOrgTreeSelect(props: ProFormTreeSelectProps & { currentItem?: any }) {
  const {
    currentItem,
    name = 'parent_id',
    label = '上级部门',
    rules,
    fieldProps,
    disabled,
    placeholder,
  } = props;
  const [treeData, setTreeData] = useState<any>([]);
  const getTreeData = useCallback(
    (value: any) => {
      return request(orgApi.getList, {
        method: 'GET',
        params: {
          name: value || '',
          tenant_id: 1,
        },
      }).then((res: any) => {
        if (res.code === 200) {
          let list = res?.data?.list || [];
          setTreeData(list);
          return list as RequestOptionsType[];
        } else {
          return [] as RequestOptionsType[];
        }
      });
    },
    [setTreeData],
  );
  useEffect(() => {
    getTreeData('');
  }, []);

  return (
    <ProFormTreeSelect
      name={name}
      label={label}
      debounceTime={500}
      rules={rules}
      disabled={disabled}
      fieldProps={{
        treeData: treeData,
        treeNodeFilterProp: 'label',
        defaultValue: '',
        treeDefaultExpandAll: true,
        showSearch: true,
        fieldNames: {
          label: 'name',
          value: 'id',
          children: 'children',
        },
        onSearch: (value) => {
          getTreeData(value);
        },
        placeholder: placeholder || `请选择${label}`,
        dropdownStyle: { maxHeight: '300px', overflow: 'auto' },
        style: {
          width: 440,
          height: 40,
        },
        ...fieldProps,
      }}
    />
  );
}
