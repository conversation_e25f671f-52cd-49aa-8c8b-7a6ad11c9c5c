/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-23 23:42:10
 * @LastEditTime: 2025-02-13 22:07:08
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { memo, useEffect, useState } from 'react';

import icons from './assets';
import MENU_ICONS from '../../assets/svg';
interface IProps {
  className?: string;
  id: string;
  icon: string;
  onClick: () => void;
  data: any;
  name: string | keyof typeof MENU_ICONS;
}
export const XcMenuItem = memo((props: IProps) => {
  const { className, id, icon, onClick, name, data } = props;
  const NormalIcon = MENU_ICONS[icon as keyof typeof MENU_ICONS] || null;
  const ActiveIcon = MENU_ICONS[`${icon}Active` as keyof typeof MENU_ICONS] || null;
  const activeClass = 'bg-black bg-opacity-15 font-bold';
  const hoverClass = 'hover:font-bold';
  const [active, setActive] = useState(false);
  const [hover, setHover] = useState(false);

  useEffect(() => {
    if (location?.pathname.indexOf(data.path) > -1) {
      setActive(true);
    } else {
      setActive(false);
    }
  }, [data]);

  const styleActive = hover || active;

  return (
    <div
      key={id}
      className={`cursor-pointer text-[14px] px-[10px] gap-[10px] rounded-[8px] flex h-[40px] items-center text-white ${className || ''} ${active ? activeClass : ''} ${hoverClass}`}
      onClick={onClick}
      onMouseEnter={() => setHover(true)}
      onMouseLeave={() => setHover(false)}
    >
      {!styleActive && NormalIcon ? <NormalIcon className={'size-[24px]'} /> : null}
      {styleActive && ActiveIcon ? <ActiveIcon className={'size-[24px]'} /> : null}
      <span>{name}</span>
    </div>
  );
});
