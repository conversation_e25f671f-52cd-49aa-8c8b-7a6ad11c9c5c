import { XcModalForm } from '@/components/XcModal/XcModalForm';
import { ProForm, ProFormText } from '@ant-design/pro-components';
import { request, useRequest } from '@umijs/max';
import { Button, Form, message, Table, Typography } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import { orgApi } from '@/api/org';
import { ColumnsType } from 'antd/es/table';
import { XcInput } from '@/components/XcInput';
import { NAME_REG_EXP } from '@/constants/regex';
import XcOrgTreeSelect from './XcOrgTreeSelect';
import XcAntdTable from '@/components/XcAntdTable/XcAntdTable';
import { IconClose } from '@/components/Icon/IconClose';
import { IconSearch } from '@/components/Icon/IconSearch';
import PermissionWrap from '@/components/PermissionWrap';
import { STATIC_PERMISSION_CODE } from '@/utils/permission';
import { IconAdd } from '@/assets/svg';

const { Text } = Typography;
enum MODAL_TYPE {
  none = 0,
  add = 1,
  edit = 2,
  delete = 3,
  unbind = 4, // 解绑
  deleteRemind = 5, // 提示解绑用户
}
export default () => {
  const [modalType, setModalType] = useState<MODAL_TYPE>(MODAL_TYPE.none);
  const [currentItem, setCurrentItem] = useState<Record<string, any>>({});
  const [dataSource, setDataSource] = useState<any>([]);
  const formRef = useRef<any>(null);
  const paginationRef = useRef({
    current: 1,
    pageSize: 50,
    total: 0,
  });

  /**
   * 部门列表请求接口
   */
  const orgListRequest = useRequest(
    (data?: any, headers?: any) => ({
      url: orgApi.getList,
      params: { ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: { total: 0, page: 1, pageSize: 50, list: [] } },
      throwOnError: true,
    },
  );

  const requestTable = async () => {
    const name = formRef.current?.getFieldValue('name');
    const { current, pageSize } = paginationRef.current;
    const queryPayload = {
      name: name || '',
      page: current,
      page_size: pageSize,
      tenant_id: 1,
    };
    const result = await orgListRequest.run(queryPayload);
    if (result.code === 200) {
      paginationRef.current.total = result?.data.total || 0;
      setDataSource(result.data.list);
    } else {
      setDataSource([]);
    }
  };

  useEffect(() => {
    requestTable();
  }, []);

  const closeModal = () => {
    setModalType(MODAL_TYPE.none);
  };
  const onOpenModal = (open: boolean) => {
    if (!open) {
      closeModal();
    }
  };

  const showModal = (item: any, type: MODAL_TYPE) => {
    if (item.parent_id === 0) {
      item.parent_id = null;
    }
    setCurrentItem(item);
    setModalType(type);
  };
  const onAddOrg = async (data: any) => {
    request(orgApi.postOrg, {
      data: { ...data, tenant_id: 1 },
      method: 'POST',
    }).then((res) => {
      if (res.code === 200) {
        message.info('新增成功');
        closeModal();
        requestTable();
        return true;
      } else {
        message.error(res.message || '新增失败');
        return false;
      }
    });
  };
  const onEditOrg = async (data: any) => {
    return request(orgApi.putOrg(data.id), {
      data: {
        ...data,
        tenant_id: 1,
      },
      method: 'PUT',
    }).then((res) => {
      if (res.code === 200) {
        message.info('修改成功');
        closeModal();
        requestTable();
        return true;
      } else {
        message.error(res.message || '修改失败');
        return false;
      }
    });
  };
  const onDeleteOrg = async () => {
    return request(orgApi.deleteOrg(currentItem.id), {
      method: 'DELETE',
      params: {
        tenant_id: 1,
      },
    }).then((res: any) => {
      if (res.code === 200) {
        message.info('删除成功');
        closeModal();
        requestTable();
        return true;
      } else {
        message.error(res.message || '删除失败');
        return true;
      }
    });
  };

  const onUnbindOrg = async () => {
    return request(orgApi.deleteBindOrg(currentItem.id), {
      method: 'DELETE',
      params: {
        tenant_id: 1,
      },
    }).then((res: any) => {
      if (res.code === 200) {
        message.info('解绑成功');
        closeModal();
        requestTable();
        return true;
      } else {
        message.info(res.message || '解绑失败');
        return false;
      }
    });
  };

  const columns = [
    {
      title: '部门名称',
      dataIndex: 'id',
      key: 'id',
      render: (_, item: { name: string }) => {
        return (
          <Text className="text-txt-main bg-white" ellipsis={{ tooltip: item.name }}>
            {item.name}
          </Text>
        );
      },
    },
    {
      title: '部门编号',
      dataIndex: 'code',
      key: 'code',
      render: (_, item: any) => {
        return <div className="text-txt-main">{item.code}</div>;
      },
    },
    {
      title: '部门用户数',
      dataIndex: 'user_count',
      key: 'user_count',
      render: (_, item: any) => {
        return <div className="text-txt-main">{item.user_count}</div>;
      },
    },
    {
      title: '操作',
      width: 242,
      align: 'center',
      render: (_, item: any) => {
        return (
          <div className={'flex items-center justify-end gap-[16px] w-full'}>
            <PermissionWrap accessCode={STATIC_PERMISSION_CODE.创建部门}>
              <Button
                type="link"
                className="text-txt-main px-[0px]"
                onClick={() => showModal({ parent_id: item.id }, MODAL_TYPE.add)}
              >
                新增子部门
              </Button>
            </PermissionWrap>
            <PermissionWrap accessCode={STATIC_PERMISSION_CODE.解绑部门用户}>
              <Button
                type="link"
                className={`text-txt-main px-[0px] ${item?.children?.length > 0 || item.user_count <= 0 ? 'hidden' : ''}`}
                onClick={() => showModal(item, MODAL_TYPE.unbind)}
              >
                解绑
              </Button>
            </PermissionWrap>
            <PermissionWrap accessCode={STATIC_PERMISSION_CODE.更新部门}>
              <Button
                type="link"
                className="text-txt-main px-[0px]"
                onClick={() => showModal(item, MODAL_TYPE.edit)}
              >
                修改
              </Button>
            </PermissionWrap>
            <PermissionWrap accessCode={STATIC_PERMISSION_CODE.删除部门}>
              <Button
                type="link"
                className={`text-txt-main px-[0px] ${item?.children?.length > 0 ? 'hidden' : ''}`}
                onClick={() => {
                  if (item.user_count > 0) {
                    showModal(item, MODAL_TYPE.deleteRemind);
                  } else {
                    showModal(item, MODAL_TYPE.delete);
                  }
                }}
              >
                删除
              </Button>
            </PermissionWrap>
          </div>
        );
      },
    },
  ];
  return (
    <div className="flex-1 flex ">
      <div className="w-full  mt-[20px]">
        <div className="flex">
          <div className="flex-1">
            <ProForm
              layout="horizontal"
              formRef={formRef}
              submitter={false}
              onFinish={requestTable}
            >
              <Form.Item name="name" className="mb-[20px]">
                <XcInput
                  placeholder="搜索部门名称"
                  prefix={<IconSearch className="ml-[16px] mr-[8px] size-[20px] text-txt-sub " />}
                  allowClear={{
                    clearIcon: (
                      <IconClose
                        onClick={() => {
                          requestTable();
                        }}
                      />
                    ),
                  }}
                  style={{
                    width: '320px',
                    height: '40px',
                    borderRadius: '8px',
                  }}
                  onPressEnter={requestTable}
                  onBlur={requestTable}
                  onChange={requestTable}
                />
              </Form.Item>
            </ProForm>
          </div>
          <PermissionWrap accessCode={STATIC_PERMISSION_CODE.创建部门}>
            <Button
              type="primary"
              className={
                'w-[124px] h-[40px] rounded-[8px] bg-btn-blue text-txt-white text-[14px] mb-[20px]'
              }
              icon={<IconAdd className="!align-middle" />}
              onClick={() => {
                showModal({}, MODAL_TYPE.add);
              }}
            >
              新增部门
            </Button>
          </PermissionWrap>
        </div>
        <div className="h-[calc(100vh-70px-60px-80px-32px)]">
          <XcAntdTable
            loading={orgListRequest.loading}
            columns={columns as ColumnsType}
            rowKey="id"
            pagination={{
              ...paginationRef.current,
              onChange: (page: number, pageSize: number) => {
                paginationRef.current.current = page;
                paginationRef.current.pageSize = pageSize;
                requestTable();
              },
            }}
            dataSource={dataSource}
          />
        </div>
      </div>
      <XcModalForm
        title="新增"
        width={500}
        open={modalType === MODAL_TYPE.add}
        initialValues={currentItem}
        modalProps={{
          zIndex: 1000,
          destroyOnClose: true,
          centered: true,
        }}
        onOpenChange={onOpenModal}
        onFinish={onAddOrg}
        submitter={{
          render: (props) => {
            return [
              <Button className="xc-form-button-cancel" onClick={closeModal} key="cancel">
                取消
              </Button>,
              <Button
                type="primary"
                htmlType="submit"
                className={'xc-form-button-submit'}
                key="ok"
                onClick={props.form?.submit}
              >
                保存
              </Button>,
            ];
          },
        }}
      >
        <ProFormText
          name="code"
          label={'部门编号'}
          className="h-[40px]"
          disabled={modalType === MODAL_TYPE.edit}
          rules={[
            {
              required: true,
              message: '请填写部门编号',
            },
            {
              pattern: NAME_REG_EXP,
              message: '请填写正确的部门编号',
            },
          ]}
        ></ProFormText>
        <ProFormText
          name="name"
          label={'部门名称'}
          className="h-[40px]"
          rules={[
            {
              required: true,
              message: '请填写部门名称',
            },
            {
              pattern: NAME_REG_EXP,
              message: '请填写正确的部门名称',
            },
          ]}
        ></ProFormText>
        {currentItem.parent_id > 0 ? (
          <XcOrgTreeSelect
            currentItem={null}
            disabled
            rules={[
              {
                required: currentItem.parent_id > 0 ? true : false,
                message: '请选择上级部门',
              },
            ]}
          ></XcOrgTreeSelect>
        ) : (
          <ProFormText
            label={'上级部门'}
            className="h-[40px]"
            disabled
            initialValue={'添加后默认为最高级组织架构'}
            placeholder={'添加后默认为最高级组织架构'}
          ></ProFormText>
        )}
      </XcModalForm>
      <XcModalForm
        title="修改"
        width={500}
        open={modalType === MODAL_TYPE.edit}
        initialValues={currentItem}
        modalProps={{
          zIndex: 1000,
          destroyOnClose: true,
          centered: true,
        }}
        onOpenChange={onOpenModal}
        onFinish={(data) => {
          return onEditOrg({
            ...currentItem,
            ...data,
          });
        }}
        submitter={{
          render: (props) => {
            return [
              <Button className="xc-form-button-cancel" onClick={closeModal} key="cancel">
                取消
              </Button>,
              <Button
                type="primary"
                htmlType="submit"
                className={'xc-form-button-submit'}
                key="ok"
                onClick={props.form?.submit}
              >
                确定
              </Button>,
            ];
          },
        }}
      >
        <ProFormText
          name="name"
          label={'部门名称'}
          className="h-[40px]"
          rules={[
            {
              required: true,
              message: '请填写部门名称',
            },
            {
              pattern: NAME_REG_EXP,
              message: '请填写正确的部门名称',
            },
          ]}
        ></ProFormText>
        {currentItem.parent_id ? (
          <XcOrgTreeSelect currentItem={currentItem} disabled></XcOrgTreeSelect>
        ) : (
          <ProFormText
            label={'上级部门'}
            className="h-[40px]"
            disabled
            initialValue={'最高级组织架构'}
            placeholder={'最高级组织架构'}
          ></ProFormText>
        )}
      </XcModalForm>
      <XcModalForm
        title="删除"
        width={500}
        open={modalType === MODAL_TYPE.deleteRemind}
        modalProps={{
          zIndex: 1000,
          destroyOnClose: true,
          centered: true,
        }}
        onOpenChange={onOpenModal}
        onFinish={() => {
          setTimeout(() => {
            showModal(currentItem, MODAL_TYPE.unbind);
          }, 200);
          return Promise.resolve(true);
        }}
      >
        <div className="my-[40px]">请先解绑“{currentItem.name}”部门下的客户，再删除。</div>
      </XcModalForm>
      <XcModalForm
        title="确认"
        width={500}
        open={modalType === MODAL_TYPE.delete}
        modalProps={{
          zIndex: 1000,
          destroyOnClose: true,
          centered: true,
        }}
        onOpenChange={onOpenModal}
        onFinish={onDeleteOrg}
      >
        <div className="my-[40px]">
          删除“{currentItem.name}”部门后，该部门下的用户将无法查看相关数据， 请谨慎操作！确定删除？
        </div>
      </XcModalForm>
      <XcModalForm
        title="解绑"
        width={500}
        open={modalType === MODAL_TYPE.unbind}
        modalProps={{
          zIndex: 1000,
          destroyOnClose: true,
          centered: true,
        }}
        onOpenChange={onOpenModal}
        onFinish={onUnbindOrg}
      >
        <div className="my-[40px]">
          解绑“{currentItem.name}”部门后，该部门下的用户（{currentItem.user_count}
          人）将无法查看相关数据，请谨慎操作！确定解绑？
        </div>
      </XcModalForm>
    </div>
  );
};
