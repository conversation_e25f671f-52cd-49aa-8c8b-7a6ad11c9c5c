import React, { useMemo, useRef } from 'react';
import Panel from '@/components/Panel';
import EChartsCommon from '@/components/Charts/Charts/EChartsCommon';
import { ReactComponent as IconHabitatRank } from '../assets/habitat-rank.svg';
import { <PERSON><PERSON>, Spin } from 'antd';
import { statisticsAPI } from '@/api';
import { useRequest } from '@umijs/max';
import { EChartsOption } from 'echarts';
import DataSearchForm from '../../components/DataSearchForm';
import { getMosColor, getMosName } from '@/utils/mosqutio.util';
import { SENSOR_TYPE, SENSOR_TYPE_NAME } from '@/constants/common';

export default function EnvironmentAnalysis(props: any) {
  const { menu } = props;
  const queryRef = useRef<any>({});
  const {
    run: getEnvironmentAnalysis,
    data: environmentAnalysisRes,
    loading,
  } = useRequest(
    (data?: any, headers?: any) => {
      queryRef.current = { ...data };
      return {
        url: statisticsAPI.getEnvironmentAnalysis,
        method: 'get',
        params: {
          ...queryRef.current,
          monitor_type: queryRef.current?.monitor_type || '',
          tenant_id: 1,
        },
        headers,
      };
    },
    {
      manual: true,
    },
  );
  const environmentAnalysisData = useMemo(() => {
    const list = environmentAnalysisRes?.type_groups || [];
    let result = list.slice(0);
    return result;
  }, [environmentAnalysisRes]);

  const option = useMemo((): EChartsOption => {
    let result = environmentAnalysisData || [];
    const sensorType = queryRef.current.sensor_type as keyof typeof SENSOR_TYPE;
    result = result.map((item: any) => {
      return {
        ...item,
        color: getMosColor(item.monitor_type),
        name: item.monitor_type === 'all' ? '总蚊虫密度' : getMosName(item.monitor_type) || '其他',
      };
    });
    let xLabels = [];
    let length = 0;
    let xItem = {};
    result.forEach((item: any) => {
      if (item.data_points?.length > length) {
        length = item.data_points.length;
        xItem = item;
      }
    });
    if (xItem?.data_points) {
      xLabels = xItem?.data_points.map((item: any) => {
        switch (sensorType) {
          case SENSOR_TYPE.temp:
            return item.range_max + '°C';
          case SENSOR_TYPE.hum:
            return item.range_min + '%-' + item.range_max + '%';
          case SENSOR_TYPE.wind:
            return item.range_max + 'm/s';
          case SENSOR_TYPE.light:
            return item.range_min + 'Lux-' + item.range_max + 'Lux';
          case SENSOR_TYPE.co2:
            return item.range_min + 'ppm-' + item.range_max + 'ppm';
          default:
            return item.range_max;
        }
      });
    }
    return {
      dataZoom:
        length > 32
          ? [
              {
                type: 'inside', // 滑动条类型
                show: false, // 显示滑动条
                xAxisIndex: [0], // 指定作用于 x 轴
                start: 0, // 初始显示的起始位置（百分比）
                end: 3200 / length, // 初始显示的结束位置（百分比）
                zoomLock: true,
              },
            ]
          : null,
      tooltip: {
        trigger: 'axis',
      },
      legend: {
        show: true,
        icon: 'roundRect',
        data: result.map((item: any) => item.name),
        textStyle: {
          color: '#000',
        },
        left: 'center',
        bottom: 0,
      },
      grid: [
        {
          left: 80,
          right: 80,
          top: 40,
          bottom: 40,
          containLabel: true,
        },
      ],
      xAxis: [
        {
          type: 'category',
          data: xLabels,
          nameLocation: 'end',
          name: SENSOR_TYPE_NAME[sensorType] || '',
          nameTextStyle: { color: '#797A85' },
          axisLabel: {
            rotate: 45,
          },
        },
      ],
      yAxis: [
        {
          type: 'value' as const,
          name: '蚊虫监测总数（只）',
          nameLocation: 'end',
          nameTextStyle: { color: '#797A85', align: 'center' },
        },
      ],
      series: result.map((item: any) => {
        return {
          name: item.name,
          type: 'line',
          areaStyle: {
            color: item.color,
          },
          itemStyle: {
            color: item.color,
          },
          label: {
            show: true,
            position: 'center',
          },
          symbolSize: 6,
          symbol: 'emptyCircle',
          data: item?.data_points?.map((s: any) => s.capture_count + 0),
        };
      }),
    };
  }, [environmentAnalysisRes]);

  return (
    <Panel
      title={
        <span className="flex items-center gap-2">
          <IconHabitatRank className="text-[#FC91AD]"></IconHabitatRank>
          <span>环境数据与蚊虫数量分析</span>
          <span className="text-[12px] text-[#999]"></span>
        </span>
      }
      actions={
        <Button type="primary" className="rounded-[6px] px-4 h-8 text-[14px] font-medium !hidden">
          下载
        </Button>
      }
      boxStyle={{ height: 488 }}
    >
      <DataSearchForm
        menu={menu}
        onRequest={getEnvironmentAnalysis}
        allowFields={['region', 'monitor_type', 'dateRange', 'sensor_type']}
      ></DataSearchForm>
      <div className="w-full h-[330px] flex items-center justify-center flex-1">
        <div className="w-full h-[300px]">
          {environmentAnalysisData?.length ? (
            <EChartsCommon option={option} />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              {loading ? <Spin /> : <span className="text-txt-sub">暂无数据</span>}
            </div>
          )}
        </div>
      </div>
    </Panel>
  );
}
