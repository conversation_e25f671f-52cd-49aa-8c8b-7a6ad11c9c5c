import { getLastRoute } from '@/hooks/useLastRoute';
import { getFirstRoutePath } from '@/utils/common.util';
import { useNavigate } from '@umijs/max';
import { useEffect } from 'react';
import emptyImg from '@/assets/empty.png';

export default function () {
  const navigate = useNavigate();

  const lastRoute = getLastRoute();

  useEffect(() => {
    // console.log('lastRoute', lastRoute);
    if (lastRoute) {
      navigate(lastRoute);
    } else {
      const path = getFirstRoutePath();
      if (path) {
        navigate(path);
      } else {
        // navigate('/user/login');
      }
    }
  }, []);

  return (
    <div className="size-full flex flex-col justify-center items-center text-txt-third text-[12px]">
      <img src={emptyImg} className="size-[162px]"></img>
      <div>这里空空如也~</div>
    </div>
  );
}
