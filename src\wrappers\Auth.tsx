import { IInitialState } from '@/interface/shared';
import { checkMenuPermission } from '@/utils/permission';
import { Navigate, Outlet, useModel } from '@umijs/max';

export default function Auth(props: any) {
  const { initialState } = useModel('@@initialState');
  const { user } = initialState as IInitialState;
  const { permission } = props.route || {};

  const hasAuth =
    permission?.length > 0 ? permission.some((code: string) => checkMenuPermission(code)) : true;
  if (!user) {
    return <Navigate to="/user/login" />;
  } else if (hasAuth) {
    return <Outlet />;
  } else {
    return <Navigate to="/403" />;
  }
}
