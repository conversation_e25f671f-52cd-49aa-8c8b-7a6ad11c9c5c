import { useMemoizedFn } from 'ahooks';
import { Modal } from 'antd';
import { lazy, memo, Suspense, useEffect, useMemo, useRef } from 'react';
import Draggable from 'react-draggable';

import JsonView from 'react-json-view';
interface IProps {
  editorData: any;
  onClose: () => void;
}

function JsonEditor(props: IProps) {
  const { editorData, onClose } = props;
  const modalRef = useRef<any>(null);

  const JSONResult = useMemo(() => {
    try {
      return JSON.parse(editorData.json);
    } catch (error) {
      return {};
    }
  }, [editorData]);

  return (
    <Modal
      title={
        <div className="js-editor-modal-title text-txt-main cursor-pointer">
          {editorData?.title || ''}
        </div>
      }
      open={true}
      cancelText={'关闭'}
      okButtonProps={{ style: { display: 'none' } }} // 隐藏OK按钮
      width={'70vw'}
      onCancel={onClose}
      styles={{
        mask: {
          backgroundColor: 'rgba(0, 0, 0, 0)',
        },
      }}
      modalRender={(modal) => (
        <Draggable handle=".js-editor-modal-title" nodeRef={modalRef}>
          <div ref={modalRef}>{modal}</div>
        </Draggable>
      )}
    >
      <div
        className="my-[20px] rounded-[8px] xc-scrollbar-none "
        style={{ height: 'calc(80vh - 100px)' }}
      >
        <JsonView
          style={{
            padding: '10px',
            borderRadius: '4px',
            height: '100%',
            overflowY: 'auto',
            overflowX: 'hidden',
          }}
          src={JSONResult}
          theme="monokai"
          displayDataTypes={false}
        ></JsonView>
      </div>
    </Modal>
  );
}

export default memo(JsonEditor);
