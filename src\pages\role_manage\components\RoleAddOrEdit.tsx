import { useEffect, useMemo, useRef, useState } from 'react';
import { Button, message, Space } from 'antd';
import { useRequest } from '@umijs/max';
import { ProFormInstance, ProFormItem, ProFormText } from '@ant-design/pro-components';
import { roleApi } from '@/api/role';
import { NAME_REG_EXP } from '@/constants/regex';
import { compareArrayIdList } from '@/utils/common.util';
import { DataPermissionTable, MenuPermissionTable } from './PermissionTable';
import XcAntdForm from '@/components/XcAntdForm';
import { XcDrawer } from '@/components/XcDrawer';
import DeviceTree from '@/pages/device/list/components/DeviceTree';

export const RoleAddOrEdit = (props: any) => {
  const {
    modalType,
    currentItem,
    queryTableData,
    onClose,
    menuPermissionList,
    dataPermissionList = [],
  } = props;
  const formRef = useRef<ProFormInstance>(null);
  const modalNamePrefix = modalType === 1 ? '新增' : '修改';
  const [isFormChanged, setIsFormChanged] = useState(false);
  const [dataIds, setDataIds] = useState<number[]>([]);
  const [menuIds, setMenuIds] = useState<number[]>([]);
  const [deviceIds, setDeviceIds] = useState<any>(currentItem?.device_ids || []);
  const [drawerShow, setDrawerShow] = useState(false);
  const cacheRef = useRef<any>({});

  useEffect(() => {
    if (modalType === 1) {
      setIsFormChanged(true);
    } else {
      setDataIds(currentItem.datarole_ids || []);
      setMenuIds(currentItem.role_ids || []);
      setDeviceIds(currentItem.device_ids || []);
      cacheRef.current.role_ids = currentItem.role_ids || [];
      cacheRef.current.datarole_ids = currentItem.datarole_ids || [];
      cacheRef.current.device_ids = currentItem.device_ids || [];
      setIsFormChanged(false);
    }
  }, [currentItem, modalType]);
  useEffect(() => {
    cacheRef.current.role_ids = menuIds;
    cacheRef.current.datarole_ids = dataIds;
    cacheRef.current.device_ids = deviceIds;
  }, [menuIds, dataIds, deviceIds]);

  const [drawerVisible, setDrawerVisible] = useState(true);
  const closeDrawer = () => {
    setDrawerVisible(false);
  };

  const onDrawerOpenChange = (open: boolean) => {
    if (open) {
      setDrawerShow(true);
      if (modalType !== 1) {
        formRef.current?.setFieldsValue(currentItem);
      }
    } else {
      setDrawerShow(false);
      onClose();
    }
  };

  /**
   * 新增请求接口
   */
  const roleAddRequest = useRequest(
    (data?: any, headers?: any) => ({
      method: 'POST',
      url: roleApi.postRole,
      data: { ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: null },
      throwOnError: true,
    },
  );
  /**
   * 编辑请求接口
   */
  const roleEditRequest = useRequest(
    (id, data?: any, headers?: any) => ({
      method: 'PUT',
      url: roleApi.putRole(id),
      data: { ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: null },
      throwOnError: true,
    },
  );

  // 提交编辑表单
  const onFinishEdit = async (item: any) => {
    try {
      const result = await roleEditRequest.run(currentItem.id, {
        id: currentItem.id,
        tenant_id: 1,
        ...item,
        datarole_ids: dataIds,
        role_ids: menuIds,
      });
      if (result.code === 200) {
        message.success('修改成功');
        await queryTableData();
        closeDrawer();
        return true;
      } else {
        message.error(result.message);
      }
    } catch (error) {
      console.error('🚀 ~ onEditFinish ~ error:', error);
    }

    return false;
  };
  // 提交新增用户
  const onFinishAdd = async (item: any) => {
    try {
      const result = await roleAddRequest.run({
        tenant_id: 1,
        ...item,
        datarole_ids: dataIds,
        role_ids: menuIds,
      });

      if (result.code === 200) {
        message.success('新增成功');
        await queryTableData();
        closeDrawer();
        return true;
      } else {
        message.error(result.message);
        return false;
      }
    } catch (error) {
      console.error('🚀 ~ onAddFinish ~ error:', error);
    }

    return false;
  };
  const onValuesChange = (changedValues: any, values: any) => {
    if (modalType === 2) {
      const allValues = { ...cacheRef.current, ...values };
      const keys = Object.keys(allValues);
      const changed = keys.some((key) => {
        const newValue = allValues[key];
        const preValue = currentItem[key];
        let result = newValue !== preValue;
        if (Array.isArray(newValue) || Array.isArray(preValue)) {
          result = compareArrayIdList(newValue, preValue);
        }
        if (result) {
          // console.log(key, allValues[key], currentItem[key]);
        }
        return result;
      });
      setIsFormChanged(changed);
    }
  };
  const onSubmit = () => {
    const values = formRef.current?.getFieldsValue();
    if (modalType === 1) {
      return onFinishAdd(values);
    } else {
      return onFinishEdit(values);
    }
  };

  const onDataPermissionChange = (ids: number[]) => {
    setDataIds(ids);
    const values = formRef.current?.getFieldsValue();
    cacheRef.current.datarole_ids = ids;
    onValuesChange(null, {
      ...values,
    });
    // console.log('onDataPermissionChange ', ids);
  };
  const onMenuPermissionChange = (ids: number[]) => {
    setMenuIds(ids);
    const values = formRef.current?.getFieldsValue();
    cacheRef.current.role_ids = ids;
    onValuesChange(null, {
      ...values,
    });
    // console.log('onMenuPermissionChange ', ids);
  };

  const onDeviceChange = (checkedKeys: any) => {
    // const keys = [...checkedKeys.checked];
    const keys = [...checkedKeys];
    // console.log('onDeviceCheck', checkedKeys);
    formRef.current?.setFieldValue('device_ids', keys);
    setDeviceIds(keys);
    onValuesChange(null, formRef.current?.getFieldsValue());
  };

  return (
    <XcDrawer
      title={false}
      closable={false}
      mask={false}
      maskClosable={false}
      open={drawerVisible}
      width={800}
      destroyOnClose={false}
      afterOpenChange={onDrawerOpenChange}
      onClose={closeDrawer}
      isFormChanged={isFormChanged}
    >
      <div className="h-[70px] flex items-center px-[30px] ">
        <div className="text-[20px] font-medium text-txt-main flex-1">{`${modalNamePrefix}角色`}</div>
        <Button
          type="text"
          className="text-[24px] text-txt-sub cursor-pointer"
          onClick={closeDrawer}
        >
          &times;
        </Button>
      </div>
      <div className="mt-0 border-line-split border-b-[1px] border-solid "></div>
      <div className="overflow-y-auto flex-1 xc-scrollbar-y p-[30px]">
        <XcAntdForm
          formRef={formRef}
          title={`${modalNamePrefix}角色`}
          autoFocusFirstInput
          onFinish={onSubmit}
          onValuesChange={onValuesChange}
          initialValues={currentItem}
          submitter={false}
        >
          <ProFormText
            name="code"
            label={'角色编号'}
            required={true}
            rules={[
              {
                required: true,
                message: '请填写角色编号',
              },
              {
                pattern: NAME_REG_EXP,
                message: '角色编号格式不正确',
              },
            ]}
            placeholder={'请填写角色编号'}
          />
          <ProFormText
            name="name"
            label={'角色名称'}
            rules={[
              {
                required: true,
                message: '请填写角色名称',
              },
              {
                pattern: NAME_REG_EXP,
                message: '角色名称格式不正确',
              },
            ]}
            placeholder={'请填写角色名称'}
          />
          <ProFormItem label="权限设置">
            <div className="xc-permission-box">
              <DataPermissionTable
                data={dataPermissionList[0]}
                onChange={onDataPermissionChange}
                ownList={currentItem.datarole_ids}
              ></DataPermissionTable>
              <MenuPermissionTable
                key={currentItem?.id}
                data={menuPermissionList}
                onChange={onMenuPermissionChange}
                ownList={currentItem.role_ids}
              ></MenuPermissionTable>
            </div>
          </ProFormItem>
          <ProFormText hidden name="device_ids"></ProFormText>
          <ProFormItem label="数据权限" required>
            <div className="xc-scrollbar-y xc-from-tree-box h-[430px] bg-white">
              <DeviceTree
                defaultCheckedKeys={deviceIds}
                onChange={onDeviceChange}
                visible={drawerShow}
              />
            </div>
          </ProFormItem>
        </XcAntdForm>
      </div>
      <div className="mb-0 border-line-split border-b-[1px] border-solid "></div>
      <div className="xc-form-footer">
        <Space>
          <Button className="xc-form-button-cancel" onClick={closeDrawer}>
            取消
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            className={`xc-form-button-submit  ${isFormChanged ? '' : 'opacity-30 cursor-default'}`}
            onClick={() => {
              formRef.current?.submit();
            }}
          >
            保存
          </Button>
        </Space>
      </div>
    </XcDrawer>
  );
};
