import { useEffect, useMemo, useRef, useState } from 'react';
import { Button, message, Space } from 'antd';
import { deviceAPI, userApi } from '@/api';
import { useRequest } from '@umijs/max';
import { ProFormInstance, ProFormSelect, ProFormText } from '@ant-design/pro-components';
import { compareArrayIdList } from '@/utils/common.util';
import XcAntdForm from '@/components/XcAntdForm';
import { XcDrawer } from '@/components/XcDrawer';
import { DeviceAddress } from './DeviceAddress';
import { GLOBAL_MODAL_TYPE } from '@/constants/common';
import { AREA_TYPE } from '@/constants/area-type';

export default function AddOrEdit(props: any) {
  const { modalType, currentItem, queryTableData, visible, onClose } = props;
  const [isFormChanged, setIsFormChanged] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(visible);
  const formRef = useRef<ProFormInstance>(null);
  const modalNamePrefix = modalType === 1 ? '新增' : '修改';
  const [areaSelect, setAreaSelect] = useState('');
  useEffect(() => {
    if (modalType === 1) {
      setIsFormChanged(true);
    } else {
      setIsFormChanged(false);
    }
  }, [currentItem, modalType]);

  useEffect(() => {
    if (modalType !== 1) {
      if (!AREA_TYPE.includes(currentItem.area_type)) {
        currentItem.area_select = '其他';
        setAreaSelect('其他');
      } else {
        setAreaSelect(currentItem.area_type);
        currentItem.area_select = currentItem.area_type;
      }
      formRef.current?.setFieldsValue(currentItem);
    }
  }, [currentItem]);
  const onDrawerOpenChange = (open: boolean) => {
    if (open) {
      if (modalType !== 1) {
        formRef.current?.setFieldsValue(currentItem);
      }
    } else {
      onClose();
    }
  };
  const closeDrawer = () => {
    setDrawerVisible(false);
  };
  /**
   * 新增请求接口
   */
  const userAddRequest = useRequest(
    (data?: any, headers?: any) => ({
      method: 'POST',
      url: userApi.postUser,
      data: { tenant_id: 1, ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: null },
      throwOnError: true,
    },
  );
  /**
   * 新增请求接口
   */
  const deviceAddRequest = useRequest(
    (data?: any, headers?: any) => ({
      method: 'POST',
      url: deviceAPI.device,
      data: { tenant_id: 1, ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: null },
      throwOnError: true,
    },
  );
  /**
   * 编辑请求接口
   */
  const deviceEditRequest = useRequest(
    (id, data?: any, headers?: any) => ({
      method: 'PUT',
      url: deviceAPI.deviceById(id),
      data: { ...data, tenant_id: 1 },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: null },
      throwOnError: true,
    },
  );
  // 提交新增设备
  const onFinishAdd = async (item: any) => {
    try {
      const result = await deviceAddRequest.run({
        ...item,
      });

      if (result.code === 200) {
        message.success('新增设备成功');
        await queryTableData();
        closeDrawer();
        return true;
      } else {
        message.error(result.message || '新增设备失败');
      }
    } catch (error) {
      console.error('🚀 ~ onAddFinish ~ error:', error);
    }

    return false;
  };
  // 提交编辑表单
  const onFinishEdit = async (item: any) => {
    try {
      const result = await deviceEditRequest.run(currentItem.id, {
        id: currentItem.id,
        ...item,
      });
      if (result.code === 200) {
        message.success('修改设备成功');
        await queryTableData();
        closeDrawer();
        return true;
      } else {
        message.error(result.message || '修改设备失败');
      }
    } catch (error) {
      console.error('🚀 ~ onEditFinish ~ error:', error);
    }

    return false;
  };
  const onSubmit = () => {
    const values = formRef.current?.getFieldsValue();

    if (modalType === GLOBAL_MODAL_TYPE.add) {
      return onFinishAdd(values);
    } else {
      return onFinishEdit(values);
    }
  };

  const onValuesChange = (changedValues: any, allValues: any) => {
    if (modalType === 2) {
      const keys = Object.keys(allValues);
      const changed = keys.some((key) => {
        const newValue = allValues[key];
        const preValue = currentItem[key];
        let result = newValue !== preValue;
        if (Array.isArray(newValue) || Array.isArray(preValue)) {
          result = compareArrayIdList(newValue, preValue);
        }
        if (result) {
          // console.log(key, allValues[key], currentItem[key]);
        }
        return result;
      });
      setIsFormChanged(changed);
    }
  };

  const onAreaSelectChange = (value: string) => {
    if (value === '其他') {
      formRef.current?.setFieldsValue({ area_type: '' });
    } else {
      formRef.current?.setFieldsValue({ area_type: value });
    }
    setAreaSelect(value);
  };

  return (
    <>
      <XcDrawer
        title={false}
        closable={false}
        mask={false}
        maskClosable={false}
        open={drawerVisible}
        width={600}
        destroyOnClose={false}
        afterOpenChange={onDrawerOpenChange}
        onClose={closeDrawer}
        isFormChanged={isFormChanged}
      >
        <div className="h-[70px] flex items-center px-[30px]">
          <div className="text-[20px] font-medium text-txt-main flex-1">{`${modalNamePrefix}设备`}</div>
          <Button
            type="text"
            className="text-[24px] text-txt-sub cursor-pointer"
            onClick={closeDrawer}
          >
            &times;
          </Button>
        </div>
        <div className="mt-0  border-line-split border-b-[1px] border-solid "></div>
        <div className="flex-1 xc-scrollbar-none p-[30px]">
          <XcAntdForm
            formRef={formRef}
            autoFocusFirstInput
            onFinish={onSubmit}
            onValuesChange={onValuesChange}
            submitter={{
              render: (_, dom) => null,
            }}
          >
            {/* {modalType === GLOBAL_MODAL_TYPE.edit ? (
              <ProFormText
                name={'code'}
                label={'设备编号'}
                required={true}
                disabled
                rules={[
                  {
                    required: true,
                    message: '请填写设备编号',
                  },
                  // {
                  //   pattern: /^[a-zA-Z0-9_-]+$/,
                  //   message: '设备编号格式不正确',
                  // },
                ]}
                placeholder={'请填写设备编号'}
              />
            ) : null} */}
            <ProFormText
              name={'name'}
              label={'设备名称'}
              required={true}
              rules={[
                {
                  required: true,
                  message: '请填写设备名称',
                },
                {
                  pattern: /^[a-zA-Z0-9_\-\u4e00-\u9fa5]+$/,
                  message: '设备名称格式不正确',
                },
              ]}
              placeholder={'请填写设备名称'}
            />
            {/* <ProFormText
              name={'tenant_id'}

              label={<span className="text-[12px] text-txt-sub">客户ID</span>}
              required={true}
              disabled
              initialValue={1}
              rules={[
                {
                  required: true,
                  message: '请填写客户ID',
                },
                {
                  pattern: /^\d+$/,
                  message: '请填写客户ID',
                },
              ]}
              placeholder={'请填写客户ID'}
            /> */}

            <ProFormSelect
              name={'area_select'}
              label={'生境类型'}
              options={AREA_TYPE.map((item) => ({ label: item, value: item }))}
              required={false}
              onChange={onAreaSelectChange}
              rules={[
                {
                  required: true,
                  message: '请选择生境类型',
                },
              ]}
              placeholder={'请选择生境类型'}
            />
            <div className={areaSelect === '其他' ? '' : 'hidden'}>
              <ProFormText
                name={'area_type'}
                label={'生境名称'}
                rules={
                  areaSelect === '其他'
                    ? [
                        {
                          type: 'string',
                          message: '请填写生境名称',
                        },
                        {
                          pattern: /^[a-zA-Z0-9_\-\u4e00-\u9fa5]{1,10}$/,
                          message: '生境名称格式不正确',
                        },
                      ]
                    : []
                }
                placeholder={'请填写生境名称'}
              />
            </div>
            <DeviceAddress tableRef={formRef} />
            <ProFormText
              name={'monitor_unit'}
              label={'监测单位'}
              required={false}
              rules={[
                {
                  type: 'string',
                  message: '请填写监测单位',
                },
              ]}
              placeholder={'请填写监测单位'}
            />
            <ProFormText
              name={'monitor'}
              label={'设备管理员'}
              required={false}
              fieldProps={{
                maxLength: 20,
              }}
              rules={[
                {
                  type: 'string',
                  message: '请填写设备管理员',
                },
              ]}
              placeholder={'请填写设备管理员'}
            />
            {/* <ProFormText
              name={'auditor'}
              label={'审核人'}
              required={false}
              fieldProps={{
                maxLength: 20,
              }}
              rules={[
                {
                  type: 'string',
                  message: '请填写审核人',
                },
              ]}
              placeholder={'请填写审核人'}
            />
            <ProFormText
              name={'point_manager'}
              label={'监测点负责人'}
              required={false}
              fieldProps={{
                maxLength: 20,
              }}
              rules={[
                {
                  type: 'string',
                  message: '请填写监测点负责人',
                },
              ]}
              placeholder={'请填写监测点负责人'}
            /> */}
          </XcAntdForm>
        </div>
        <div className="mb-0  border-line-split border-b-[1px] border-solid px-[30px]"></div>
        <div className="xc-form-footer">
          <Space>
            <Button className="xc-form-button-cancel" onClick={closeDrawer}>
              取消
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              className={`xc-form-button-submit  ${isFormChanged ? '' : 'opacity-30 cursor-default'}`}
              onClick={() => {
                formRef.current?.submit();
              }}
            >
              保存
            </Button>
          </Space>
        </div>
      </XcDrawer>
    </>
  );
}
