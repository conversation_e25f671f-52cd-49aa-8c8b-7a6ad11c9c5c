@tailwind base;
@tailwind components;
@tailwind utilities;


@layer components {

  .xc-form-button-cancel {
    @apply !bg-bgc-normal !text-txt-main;
  }

  .xc-form-button-submit {
    @apply !bg-btn-blue !text-txt-white;
  }

  .xc-bordered-menu-lt {
    @apply border-l-[1px] border-t-[1px] border-solid border-box-menu;
  }

  .xc-bordered-menu-rb {
    @apply border-r-[1px] border-b-[1px] border-solid border-box-menu;
  }

  .xc-permission-box {
    @apply bg-white xc-bordered-menu-lt;
  }

  .xc-permission-header {
    @apply px-[20px] py-[10px] h-[40px] items-center flex xc-bordered-menu-rb;
  }

  .xc-permission-body {
    @apply flex;
  }

  .xc-permission-menu {
    @apply flex min-w-[172px] px-[20px] py-[10px] flex-shrink-0 items-center justify-start xc-bordered-menu-rb;
  }

  .xc-permission-content {
    @apply pl-[20px] pt-[10px] pb-[2px] flex justify-start flex-wrap flex-1 xc-bordered-menu-rb items-center;
  }

  .xc-permission-checkbox {
    @apply min-w-[112px] flex mb-[8px] h-[22px] flex-nowrap text-nowrap;
  }

  .text-one-row {
    @apply text-ellipsis overflow-hidden whitespace-nowrap;
  }

  .break-all-words {
    @apply break-all break-words;
  }
}
