/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-13 15:05:09
 * @LastEditTime: 2025-02-12 11:11:50
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { styled } from '@umijs/max';
import { ConfigProvider, Select } from 'antd';
import { px2font, px2vh } from '../../../../components/LargeScreen/vw/vw';

const StyledSelect = styled(Select)`
  height: ${() => px2vh(32)};
  > .ant-select-selector {
    border: 1px solid rgba(255, 255, 255, 0.18) !important;
    background: rgba(169, 169, 169, 0.08) !important;
  }
  .ant-select-selection-item {
    font-size: ${() => px2font(14)};
  }
  .ant-select-selection-placeholder {
    font-size: ${() => px2font(14)};
  }
`;
const StyledText = styled.p`
  font-size: ${() => px2font(14)};
`;

export const XcSelect = (props: any) => {
  const { options, value, onChange, suffixIcon, placeholder } = props;
  return (
    <ConfigProvider
      theme={{
        components: {
          Select: {
            borderRadius: 6,
            lineWidth: 0,
            colorTextPlaceholder: '#FFF',
            colorText: '#FFF',
            optionSelectedBg: '#2953ff',
            optionSelectedColor: '#ffffff',
            optionSelectedFontWeight: 400,
            colorBgElevated: '#272D40',
          },
        },
      }}
    >
      <StyledSelect
        value={value}
        onChange={onChange}
        suffixIcon={suffixIcon}
        optionRender={(option) => <StyledText>{option.data.label}</StyledText>}
        placeholder={placeholder}
        options={options}
      />
    </ConfigProvider>
  );
};
