/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-11-13 00:24:40
 * @LastEditTime: 2024-11-20 16:34:36
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */

/**
 *
 * @param type 1 近1天 2 近7天 3 近30天 4 近90天
 * @returns
 */
export const queryFakeList = (type = 1) => {
  let xData = [];
  // 1. 近1天
  if (type === 1) {
    for (let i = 0; i <= 23; i++) {
      xData.push(`${i.toString().padStart(2, '0')}:00`);
    }
  }

  let valueBase = Math.random() * 300;
  let valueBase2 = Math.random() * 50;
  let data = [];
  let data2 = [];
  for (let i = 0; i < xData.length; i++) {
    let now = xData[i];
    valueBase = Math.round((Math.random() - 0.5) * 20 + valueBase);
    if (valueBase <= 0) {
      valueBase = Math.random() * 300;
    }
    data.push([now, valueBase]);

    valueBase2 = Math.round((Math.random() - 0.5) * 20 + valueBase2);
    if (valueBase2 <= 0) {
      valueBase2 = Math.random() * 50;
    }
    data2.push([now, valueBase2]);
  }

  const yData = [
    {
      name: 'Fake Data',
      type: 'line',
      smooth: true,
      symbol: 'circle',
      symbolSize: 5,
      sampling: 'average',
      itemStyle: {
        color: '#0770FF',
      },
      stack: 'a',
      areaStyle: {
        color: [
          {
            offset: 0,
            color: 'rgba(58,77,233,0.8)',
          },
          {
            offset: 1,
            color: 'rgba(58,77,233,0.3)',
          },
        ],
      },
      data: data,
    },
    {
      name: 'Fake Data',
      type: 'line',
      smooth: true,
      stack: 'a',
      symbol: 'circle',
      symbolSize: 5,
      sampling: 'average',
      itemStyle: {
        color: '#F2597F',
      },
      areaStyle: {
        color: [
          {
            offset: 0,
            color: 'rgba(213,72,120,0.8)',
          },
          {
            offset: 1,
            color: 'rgba(213,72,120,0.3)',
          },
        ],
      },
      data: data2,
    },
  ];

  return { yData, xData };
};
