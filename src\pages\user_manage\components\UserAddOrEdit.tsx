import { useEffect, useMemo, useRef, useState } from 'react';
import { But<PERSON>, Drawer, message, Space, Tree, TreeSelect } from 'antd';
import { userApi } from '@/api';
import { useRequest } from '@umijs/max';
import {
  ProFormInstance,
  ProFormItem,
  ProFormText,
  ProFormTreeSelect,
} from '@ant-design/pro-components';
import { IMAGES_DEFAULT } from '@/constants/common';
import { NAME_REG_EXP, PHONE_REG_EXP, USER_NAME_REG_EXP } from '@/constants/regex';
import { formatOrgTreeOptions } from '@/utils/format';
import { IconLock } from '@/assets/svg';
import { compareArrayIdList } from '@/utils/common.util';
import { XcAntdTree } from '@/components/XcAntdTree';
import PermissionWrap from '@/components/PermissionWrap';
import { STATIC_PERMISSION_CODE } from '@/utils/permission';
import XcAntdForm from '@/components/XcAntdForm';
import { XcDrawer } from '@/components/XcDrawer';

export const UserAddOrEdit = (props: any) => {
  const {
    modalType,
    currentItem,
    queryTableData,
    visible,
    onClose,
    allOrgList,
    allRoleList,
    showResetPassword,
  } = props;
  const [isFormChanged, setIsFormChanged] = useState(false);
  const [orgIds, setOrgIds] = useState(currentItem.org_ids || []);
  const [drawerVisible, setDrawerVisible] = useState(visible);
  const formRef = useRef<ProFormInstance>(null);
  const modalNamePrefix = modalType === 1 ? '新增' : '修改';
  const orgOptions = useMemo(() => {
    return formatOrgTreeOptions(allOrgList || []);
  }, [allOrgList]);

  useEffect(() => {
    if (modalType === 1) {
      setIsFormChanged(true);
    } else {
      setIsFormChanged(false);
    }
  }, [currentItem, modalType]);

  useEffect(() => {
    setOrgIds(currentItem.org_ids || []);
    if (modalType !== 1) {
      formRef.current?.setFieldsValue(currentItem);
    }
  }, [currentItem]);
  const onDrawerOpenChange = (open: boolean) => {
    if (open) {
      if (modalType !== 1) {
        formRef.current?.setFieldsValue(currentItem);
      }
    } else {
      onClose();
    }
  };
  const closeDrawer = () => {
    setDrawerVisible(false);
  };
  /**
   * 新增请求接口
   */
  const userAddRequest = useRequest(
    (data?: any, headers?: any) => ({
      method: 'POST',
      url: userApi.postUser,
      data: { tenant_id: 1, ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: null },
      throwOnError: true,
    },
  );
  /**
   * 编辑请求接口
   */
  const userEditRequest = useRequest(
    (id, data?: any, headers?: any) => ({
      method: 'PUT',
      url: userApi.putUser(id),
      data: { ...currentItem, ...data },
      headers,
    }),
    {
      manual: true,
      formatResult: (r) => r,
      initialData: { result: null },
      throwOnError: true,
    },
  );

  // 提交编辑表单
  const onFinishEdit = async (item: any) => {
    try {
      const result = await userEditRequest.run(currentItem.id, {
        id: currentItem.id,
        ...item,
      });
      if (result.code === 200) {
        message.success(result.message || '修改成功。');
        await queryTableData();
        closeDrawer();
        return true;
      } else {
        message.error(result.message || '修改失败，请重试。');
      }
    } catch (error) {
      console.error('🚀 ~ onEditFinish ~ error:', error);
    }

    return false;
  };
  // 提交新增用户
  const onFinishAdd = async (item: any) => {
    try {
      const result = await userAddRequest.run({
        ...item,
      });

      if (result.code === 200) {
        message.success('新增用户成功。');
        await queryTableData();
        closeDrawer();
        return true;
      } else {
        message.error(result.message || '新增失败，请重试。');
      }
    } catch (error) {
      console.error('🚀 ~ onAddFinish ~ error:', error);
    }

    return false;
  };
  const onSubmit = () => {
    const values = formRef.current?.getFieldsValue();
    // values.role_ids = values?.role_ids || []; //getQueryIdList(values?.role_ids);
    // values.orgs = values?.org_ids || []; //getQueryIdList(values?.org_ids);
    // 去掉不必要参数
    // delete values.role_ids;
    // delete values.org_ids;
    if (modalType === 1) {
      return onFinishAdd(values);
    } else {
      return onFinishEdit(values);
    }
  };

  const onValuesChange = (changedValues: any, allValues: any) => {
    if (modalType === 2) {
      const keys = Object.keys(allValues);
      const changed = keys.some((key) => {
        const newValue = allValues[key];
        const preValue = currentItem[key];
        let result = newValue !== preValue;
        if (Array.isArray(newValue) || Array.isArray(preValue)) {
          result = compareArrayIdList(newValue, preValue);
        }
        if (result) {
          // console.log(key, allValues[key], currentItem[key]);
        }
        return result;
      });
      setIsFormChanged(changed);
    }
  };
  const onOrgCheck = (checkedKeys: any, e: any) => {
    // const keys = [...checkedKeys.checked];
    const keys = [...checkedKeys];
    // console.log('onOrgCheck', checkedKeys, e);
    formRef.current?.setFieldValue('org_ids', keys);
    setOrgIds(keys);
    onValuesChange(null, formRef.current?.getFieldsValue());
  };
  return (
    <>
      {/* <XcTableMask
        visible={drawerVisible}
        setVisible={closeDrawer}
        width={160}
        isFormChanged={isFormChanged}
      ></XcTableMask> */}
      <XcDrawer
        title={false}
        closable={false}
        mask={false}
        maskClosable={false}
        open={drawerVisible}
        width={600}
        destroyOnClose={false}
        afterOpenChange={onDrawerOpenChange}
        onClose={closeDrawer}
        isFormChanged={isFormChanged}
      >
        <div className="h-[70px] flex items-center px-[30px]">
          <div className="text-[20px] font-medium text-txt-main flex-1">{`${modalNamePrefix}用户`}</div>
          <Button
            type="text"
            className="text-[24px] text-txt-sub cursor-pointer"
            onClick={closeDrawer}
          >
            &times;
          </Button>
        </div>
        <div className="mt-0  border-line-split border-b-[1px] border-solid "></div>
        <div className="flex-1 xc-scrollbar-none p-[30px]">
          <XcAntdForm
            formRef={formRef}
            autoFocusFirstInput
            onFinish={onSubmit}
            onValuesChange={onValuesChange}
            submitter={{
              render: (_, dom) => null,
            }}
          >
            <div className="mx-auto h-[95px] w-[95px] rounded-[48px] overflow-hidden">
              <img
                src={IMAGES_DEFAULT.user}
                alt="头像"
                style={{ width: '100%', height: '100%', objectFit: 'cover' }}
              />
            </div>
            {modalType === 2 ? (
              <div className="flex justify-center items-center mt-[16px] mb-[20px]">
                <PermissionWrap accessCode={STATIC_PERMISSION_CODE.重置密码}>
                  <Button
                    className="h-[18px] leading-[18px] text-txt-main"
                    type="text"
                    icon={<IconLock />}
                    onClick={showResetPassword}
                    style={{
                      backgroundColor: 'transparent!important',
                    }}
                  >
                    重置密码
                  </Button>
                </PermissionWrap>
              </div>
            ) : null}
            <ProFormText
              name="code"
              label={'用户编号'}
              required={true}
              disabled={modalType === 2}
              fieldProps={{
                maxLength: 20,
              }}
              rules={[
                {
                  required: true,
                  message: '用户编号不能为空。',
                },
                {
                  max: 20,
                  message: '用户编号超过20个字。',
                },
                {
                  pattern: NAME_REG_EXP,
                  message: '用户编号格式错误。',
                },
              ]}
              placeholder={'请输入用户编号'}
            />
            <ProFormText
              name="name"
              label={'用户名称'}
              required={true}
              fieldProps={{
                maxLength: 20,
              }}
              rules={[
                {
                  required: true,
                  message: '用户名称不能为空。',
                },
                {
                  max: 20,
                  message: '用户名称超过20个字。',
                },
                {
                  pattern: USER_NAME_REG_EXP,
                  message: '用户名称格式错误。',
                },
              ]}
              placeholder={'输入用户名称'}
            />
            <ProFormText
              name="phone"
              label={'手机号码'}
              required={true}
              initialValue=""
              placeholder={'请输入用户的手机号码'}
              rules={[
                {
                  required: true,
                  message: '手机号码不能为空',
                },
                {
                  pattern: PHONE_REG_EXP,
                  message: '手机号格式错误，请重新输入',
                },
              ]}
            />
            <ProFormText
              name="email"
              label={'用户邮箱'}
              rules={[
                {
                  required: true,
                  message: '用户邮箱不能为空',
                },
                {
                  type: 'email',
                  message: '邮箱格式错误，请重新输入',
                },
              ]}
              placeholder={'请输入用户的电子邮箱地址'}
            />
            <ProFormText hidden name="org_ids"></ProFormText>
            <ProFormItem label="所属部门">
              <div className="xc-scrollbar-y xc-from-tree-box h-[430px] bg-white">
                <XcAntdTree
                  checkable
                  defaultCheckedKeys={orgIds}
                  onCheck={onOrgCheck}
                  treeData={orgOptions}
                />
              </div>
            </ProFormItem>
            <ProFormTreeSelect
              label="用户角色"
              name="role_ids"
              fieldProps={{
                fieldNames: {
                  label: 'name',
                  value: 'id',
                  children: 'children',
                },
                multiple: true,
                treeData: allRoleList,
                treeCheckable: true,
                showCheckedStrategy: TreeSelect.SHOW_PARENT,
                placeholder: '请选择用户角色',
              }}
            />
          </XcAntdForm>
        </div>
        <div className="mb-0  border-line-split border-b-[1px] border-solid px-[30px]"></div>
        <div className="xc-form-footer">
          <Space>
            <Button className="xc-form-button-cancel" onClick={closeDrawer}>
              取消
            </Button>
            <Button
              type="primary"
              htmlType="submit"
              className={`xc-form-button-submit  ${isFormChanged ? '' : 'opacity-30 cursor-default'}`}
              onClick={() => {
                formRef.current?.submit();
              }}
            >
              保存
            </Button>
          </Space>
        </div>
      </XcDrawer>
    </>
  );
};
