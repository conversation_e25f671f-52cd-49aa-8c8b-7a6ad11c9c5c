/*
 * @Author: <PERSON><PERSON>
 * @Date: 2025-01-24 14:05:57
 * @LastEditTime: 2025-02-08 13:15:49
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 *
 */
import { LoadingOutlined } from '@ant-design/icons';
import { styled } from '@umijs/max';
import { Spin } from 'antd';

interface IProps {
  loading?: boolean;
  children?: React.ReactNode;
}

const StyledSpin = styled(Spin)`
  max-height: 100% !important;
`;

export const XcLoading = (props: IProps) => {
  const { children, loading = false } = props;
  return (
    <StyledSpin spinning={loading} indicator={<LoadingOutlined spin />}>
      {children}
    </StyledSpin>
  );
};
