/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-28 08:58:08
 * @LastEditTime: 2025-02-06 19:58:34
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
import { Drawer, DrawerProps } from 'antd';
import { DrawerStyles } from 'antd/es/drawer/DrawerPanel';
import { memo, useState } from 'react';
import XcTableMask from '../XcTableMask';

type IProps = {
  children: React.ReactNode;
  onClose: () => void;
  mask?: boolean;
  open?: boolean;
  destroyOnClose?: boolean;
  styles?: DrawerStyles;
  width?: number | string;
  editColumnWidth?: number;
  editColumnLeft?: number;
  isFormChanged?: boolean;
} & DrawerProps;

export const XcDrawer = memo((props: IProps) => {
  const {
    children,
    width,
    onClose,
    destroyOnClose = false,
    afterOpenChange,
    mask = false,
    open,
    isFormChanged = false,
    styles,
    editColumnWidth,
    editColumnLeft,
    rootClassName,
    ...others
  } = props;

  const onDrawerOpenChange = (open: boolean) => {
    afterOpenChange?.(open);
  };

  return (
    <>
      <XcTableMask
        visible={open || false}
        setVisible={onClose}
        width={editColumnWidth}
        left={editColumnLeft}
        isFormChanged={isFormChanged}
      ></XcTableMask>
      <Drawer
        title={false}
        closable={false}
        mask={mask}
        open={open}
        width={width}
        destroyOnClose={destroyOnClose}
        afterOpenChange={onDrawerOpenChange}
        onClose={onClose}
        rootClassName={'XxcDrawer ' + rootClassName}
        {...others}
      >
        {children}
      </Drawer>
    </>
  );
});
