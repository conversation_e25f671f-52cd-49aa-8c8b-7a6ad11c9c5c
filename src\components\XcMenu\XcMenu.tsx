/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-23 23:41:50
 * @LastEditTime: 2025-02-06 19:56:50
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */

import { memo, useMemo } from 'react';
import { XcMenuItem } from './XcMenuItem';
import { filterRoutesWithPermission } from '@/utils/permission';

interface IProps {
  menus: any[];
  onSelect: (key: string, path: string) => void;
}

export const XcMenu = memo((props: IProps) => {
  const { menus, onSelect } = props;

  const renderMenus = useMemo(() => {
    return filterRoutesWithPermission(menus);
  }, [menus]);

  return (
    <>
      {renderMenus.map((item) => {
        return (
          <div key={item.id}>
            {item.name === '基础设置' ? (
              <div className="m-[10px_0px] h-[1px] bg-[rgb(148,155,170)] bg-opacity-[15%]"></div>
            ) : null}
            <div key={item.id} className="flex flex-col " data-id={item.id}>
              <div className={'h-[28px] flex items-center px-[10px] '}>
                <span className="text-white text-opacity-30 text-[12px] font-normal whitespace-nowrap ">
                  {item.name}
                </span>
              </div>
              <div className="flex flex-col ">
                {item.children?.map((child: any) => {
                  if (child.hide) {
                    return null;
                  }
                  return (
                    <XcMenuItem
                      key={child.id}
                      id={child.id}
                      name={child.name}
                      icon={child.icon}
                      data={child}
                      onClick={() => onSelect(child.id, child.path)}
                    />
                  );
                })}
              </div>
            </div>
          </div>
        );
      })}
    </>
  );
});
