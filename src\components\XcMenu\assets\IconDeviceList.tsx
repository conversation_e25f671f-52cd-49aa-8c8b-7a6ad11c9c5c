/*
 * @Author: <PERSON><PERSON>
 * @Date: 2024-12-24 00:06:23
 * @LastEditTime: 2025-02-13 22:01:06
 * @LastEditors: <PERSON><PERSON>
 * @Description:
 */
const IconDeviceList = (props: any) => {
  const { className, type, ...rest } = props;

  if (type === 'primary') {
    return (
      <svg
        className={`xc-svg ${className ?? ''}`}
        {...rest}
        width="24"
        height="24"
        viewBox="0 0 24 24"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12 3C7.58214 3 4 6.58214 4 11C4 15.4179 7.58214 19 12 19C16.4179 19 20 15.4179 20 11C20 6.58214 16.4179 3 12 3ZM12 5C12.4732 5 12.8571 5.38393 12.8571 5.85714C12.8571 6.33036 12.4732 6.71429 12 6.71429C11.5268 6.71429 11.1429 6.33036 11.1429 5.85714C11.1429 5.38393 11.5268 5 12 5ZM12 15C9.79107 15 8 13.2089 8 11C8 8.79107 9.79107 7 12 7C14.2089 7 16 8.79107 16 11C16 13.2089 14.2089 15 12 15ZM14 11C14 12.1046 13.1046 13 12 13C10.8954 13 10 12.1046 10 11C10 9.89543 10.8954 9 12 9C13.1046 9 14 9.89543 14 11ZM7.43016 18.8856C7.09082 18.648 6.62318 18.7306 6.38564 19.0699C6.1481 19.4092 6.23063 19.8769 6.56997 20.1144C8.37247 21.3762 13.0679 23.168 17.4302 20.1144C17.7695 19.8769 17.852 19.4092 17.6145 19.0699C17.377 18.7306 16.9093 18.648 16.57 18.8856C12.9322 21.432 8.96099 19.9572 7.43016 18.8856Z"
          fill="#0080FF"
        />
      </svg>
    );
  }

  return (
    <svg
      className={`xc-svg ${className ?? ''}`}
      {...rest}
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7 19.5C8.66667 20.6667 13 22.3 17 19.5"
        stroke="#667085"
        strokeWidth="1.5"
        strokeLinecap="round"
      />
      <path
        d="M4.75 11C4.75 6.99636 7.99636 3.75 12 3.75C16.0036 3.75 19.25 6.99636 19.25 11C19.25 15.0036 16.0036 18.25 12 18.25C7.99636 18.25 4.75 15.0036 4.75 11Z"
        stroke="#667085"
        strokeWidth="1.5"
      />
      <path
        d="M8.75 11C8.75 9.20529 10.2053 7.75 12 7.75C13.7947 7.75 15.25 9.20529 15.25 11C15.25 12.7947 13.7947 14.25 12 14.25C10.2053 14.25 8.75 12.7947 8.75 11Z"
        stroke="#667085"
        strokeWidth="1.5"
      />
      <path
        d="M11.6464 6.35355C11.5527 6.25979 11.5 6.13261 11.5 6C11.5 5.86739 11.5527 5.74021 11.6464 5.64645C11.7402 5.55268 11.8674 5.5 12 5.5C12.1326 5.5 12.2598 5.55268 12.3536 5.64645C12.4473 5.74022 12.5 5.86739 12.5 6C12.5 6.13261 12.4473 6.25979 12.3536 6.35355C12.2598 6.44732 12.1326 6.5 12 6.5C11.8674 6.5 11.7402 6.44732 11.6464 6.35355Z"
        fill="#667085"
        stroke="#667085"
      />
    </svg>
  );
};

export default IconDeviceList;
